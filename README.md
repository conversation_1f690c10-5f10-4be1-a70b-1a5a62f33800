# Attack on Titan: Survive

A survive.io inspired game based on the Attack on Titan anime/manga series. Fight against waves of titans using ODM (Omni-Directional Mobility) gear in this action-packed survival game. Features both **third-person** and **top-down** view modes for different gameplay experiences!

## Features

### 🎮 Core Gameplay
- **Dual View Modes**: Switch between third-person and top-down perspectives
- **Survive.io Style Combat**: Action survival gameplay with immersive 3D feel
- **Wave-based Progression**: Face increasingly difficult waves of titans
- **Character Selection**: Choose between <PERSON> or Mi<PERSON><PERSON>, each with unique sprites
- **Experience System**: Level up to become stronger and unlock new abilities

### ⚔️ Combat System
- **ODM Gear Mechanics**: Use your mouse to grapple around the battlefield
- **Combo System**: Chain attacks for bonus damage and score
- **Attack Invulnerability**: Cannot take damage while attacking (golden glow effect)
- **Enemy Health Bars**: Visual health indicators above all titans
- **Multiple Attack Types**: Space bar for melee attacks
- **Dynamic Difficulty**: Titans become stronger with each wave

### 🎵 Audio & Visuals
- **Immersive Soundtrack**: Multiple background music tracks that rotate
- **Sound Effects**: Grappling, combat, and environmental sounds
- **Particle Effects**: Visual feedback for hits and grappling
- **Parallax Backgrounds**: Multi-layered backgrounds for depth
- **Perspective Scaling**: Objects appear smaller when further away for 3D depth
- **Enhanced Titans**: Larger, more imposing enemy sprites
- **Character Animations**: Sprite-based animations for all characters

### 📊 Progression System
- **Leveling**: Gain experience by defeating titans
- **Health Upgrades**: Increase max health as you level up
- **Damage Scaling**: Attack damage increases with level
- **Wave Bonuses**: Extra rewards for completing waves

## Controls

### Menu Navigation
- **Arrow Keys / A,D**: Select character (Levi or Mikasa)
- **Space**: Start game
- **Mouse Click**: Click on character portraits to select

### In-Game Controls

#### Third-Person Mode (Default)
- **WASD**: Move (W=Forward, S=Backward, A/D=Strafe)
- **Mouse**: Look around (mouse captured for smooth looking)
- **Left Mouse Button**: Grapple to buildings directly ahead (ray-cast in facing direction)
- **Right Mouse Button**: Release grapple early
- **Space**: Attack nearby titans
- **V**: Toggle between third-person and top-down view
- **ESC**: Pause game

#### Top-Down Mode
- **WASD / Arrow Keys**: Move your character
- **Left Mouse Button**: Grapple to target location (consumes gas)
- **Right Mouse Button**: Release grapple early
- **Space**: Attack nearby titans
- **V**: Toggle between third-person and top-down view
- **ESC**: Pause game

### Pause Menu
- **ESC**: Resume game
- **M**: Return to main menu

### Game Over Screen
- **R**: Restart game
- **M**: Return to main menu
- **ESC**: Quit game

## Game Mechanics

### ODM Gear System
- **Gas Management**: Limited gas supply that regenerates over time
- **Grappling Range**: Limited range for grappling hooks
- **Speed Boost**: Grappling provides faster movement than walking
- **Strategic Usage**: Use grappling to escape danger or close distance

### Combat Mechanics
- **Attack Range**: Melee attacks have limited range that increases with combo
- **Combo System**: Consecutive hits increase damage and score
- **Attack Invulnerability**: Complete immunity to damage while attacking
- **Invulnerability Frames**: Brief invincibility after taking damage
- **Enemy Health Bars**: Color-coded health indicators (Green > Yellow > Red)
- **Visual Combat Feedback**: Golden glow effect during attacks
- **Titan Behavior**: Titans move toward player and attack when close

### Wave System
- **Progressive Difficulty**: Each wave spawns more and stronger titans
- **Wave Completion**: Defeat required number of titans to advance
- **Bonus Rewards**: Score and experience bonuses for wave completion
- **Titan Variety**: Different titan types with varying stats

### True Third-Person 3D System
- **Behind-the-Character Camera**: Camera positioned behind and above the player
- **Fixed Camera Distance**: Camera maintains consistent distance from player
- **3D World Projection**: All objects rendered in proper 3D perspective
- **Mouse Look Controls**: Rotate player and camera together with mouse
- **Cinematic Gameplay**: Watch your character perform ODM maneuvers
- **Proper Depth Perception**: Objects scale and position based on distance from camera
- **Ground-Level Perspective**: Camera looks down at the action from above
- **Sky and Textured Ground**: Realistic environment with horizon line
- **Sprite-Based Buildings**: Buildings use actual sprites from your sprite sheets
- **Building Grappling**: Strategic grappling to buildings in the world
- **Enhanced Titan Design**: Legs hidden for more imposing appearance

### Enhanced Building System
- **Larger Buildings**: Buildings are 50% bigger than before for better visibility
- **Sprite-Based Architecture**: Buildings use larger sections from your existing sprite sheets
- **Multiple Building Types**: Uses bg_buildings, midground, foreground, and shiganshina sprites
- **3x2 Grid Sections**: Each sprite divided into larger sections for more detailed buildings
- **Strategic Placement**: 12 well-spaced buildings throughout the world, avoiding clustering
- **Distance-Based Scaling**: Buildings get dramatically bigger as you approach them
- **Directional Grappling**: Grapple only in the direction you're looking (ray-casting)
- **Smart Spacing**: Minimum 400-unit distance between buildings for balanced gameplay

### Perspective System (Top-Down Mode)
- **3D Depth Effect**: Objects appear smaller when positioned higher on screen (further away)
- **Dynamic Scaling**: All sprites, health bars, and effects scale with perspective
- **Enhanced Immersion**: Creates a pseudo-3D environment in 2D space
- **Larger Titans**: Enemies are 50% bigger than before for more imposing presence
- **Scaled UI Elements**: Health bars and grappling indicators adapt to perspective

## Scoring System

### Base Points
- **Titan Kill**: 100 points base
- **Combo Bonus**: +25 points per combo level
- **Wave Bonus**: +10 points × wave number
- **Wave Completion**: +50 points × wave number

### Experience Gains
- **Titan Kill**: 20 + (5 × wave number) XP
- **Wave Completion**: 10 × wave number XP
- **Level Up Benefits**: +20 max health, +5 attack damage, full heal

## Assets Used

### Images
- `AOT_loadingscreen.png` - Main menu background
- `levi.png` - Levi character sprite sheet
- `mikasa.png` - Mikasa character sprite sheet
- `attack-on-titan-beast-titan-1.png` - Beast titan sprite
- `titan.png` - Regular titan sprite
- `AOT Background buildings.png` - Background layer
- `midground.png` - Middle background layer
- `foreground.png` - Front background layer
- `shiganshina.png` - Base background layer

### Audio
- `menu_ost.mp3` - Main menu music
- `game_ost(1).mp3`, `game_ost(2).mp3`, `game_ost(3).mp3` - Game background music
- `grapple.mp3` - ODM gear grappling sound
- `hit.mp3` - Attack/hit sound effect
- `beast_hit.mp3` - Titan hit sound effect
- `jump.mp3` - Level up sound effect

## Technical Details

### Requirements
- Python 3.7+
- Pygame 2.0+
- All asset files in the same directory as the game

### Performance
- Target FPS: 60 (gameplay), 30 (menu)
- World Size: 6000×6000 pixels
- Screen Resolution: 1000×700 pixels
- Particle System: Dynamic particle effects for visual feedback

### File Structure
```
malek-final/
├── tester.py              # Main game file
├── README.md              # This file
├── AOT_loadingscreen.png  # Menu background
├── levi.png               # Levi sprite sheet
├── mikasa.png             # Mikasa sprite sheet
├── attack-on-titan-beast-titan-1.png  # Beast titan
├── titan.png              # Regular titan
├── AOT Background buildings.png        # Background
├── midground.png          # Background layer
├── foreground.png         # Background layer
├── shiganshina.png        # Background layer
├── menu_ost.mp3           # Menu music
├── game_ost(1).mp3        # Game music 1
├── game_ost(2).mp3        # Game music 2
├── game_ost(3).mp3        # Game music 3
├── grapple.mp3            # Grapple sound
├── hit.mp3                # Hit sound
├── beast_hit.mp3          # Beast hit sound
└── jump.mp3               # Jump/level up sound
```

## How to Play

1. **Start the Game**: Run `python tester.py`
2. **Choose Character**: Select Levi or Mikasa from the main menu
3. **Survive**: Use WASD to move, left-click to grapple, right-click to release grapple, space to attack
4. **Level Up**: Defeat titans to gain experience and become stronger
5. **Progress**: Complete waves to face greater challenges
6. **Master ODM Gear**: Use grappling strategically to outmaneuver titans

## Tips for Success

- **Manage Gas**: Don't waste ODM gear gas - let it regenerate
- **Use Combos**: Chain attacks for maximum damage and score
- **Stay Mobile**: Use grappling to avoid being surrounded
- **Level Strategy**: Focus on leveling up early for better survivability
- **Wave Planning**: Clear waves efficiently to maximize bonuses

## Troubleshooting

### Common Issues

**Game won't start:**
- Ensure Python 3.7+ is installed
- Install pygame: `pip install pygame`
- Verify all asset files are in the same directory as `tester.py`

**No sound:**
- Check that audio files (.mp3) are present
- Verify system audio is working
- Some systems may require additional audio codecs

**Performance issues:**
- Close other applications to free up system resources
- The game targets 60 FPS but will adapt to your system's capabilities

**Sprite/Animation errors:**
- Ensure sprite files (levi.png, mikasa.png, etc.) are not corrupted
- Check that image files are in PNG format

### System Requirements
- **OS**: Windows, macOS, or Linux
- **Python**: 3.7 or higher
- **RAM**: 512MB minimum
- **Storage**: 50MB for game and assets
- **Audio**: Any system with audio output

### Known Limitations
- Maximum level cap is 10
- World size is limited to 6000×6000 pixels
- Maximum of 15 titans can be active simultaneously

Enjoy defending humanity against the titan threat!
