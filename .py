import pygame
import random
import sys
import math

# Initialize pygame
pygame.init()
pygame.mixer.init()

# Game constants
BASE_SCREEN_WIDTH = 1000
BASE_SCREEN_HEIGHT = 800
FPS = 60
GRAVITY = 2
JUMP_POWER = -25
PLAYER_SPEED = 5
GRAPPLE_SPEED = 15
SCROLL_THRESHOLD = 100
PARALLAX_FACTOR = 0.5
PLAYER_HEALTH = 100
BEAST_HEALTH = 200
ENEMY_HEALTH = 20
BOULDER_DAMAGE = 20
ATTACK_DAMAGE = 10
GRAPPLE_DAMAGE = 5
GRAPPLE_HIT_COOLDOWN = 30
SCORE_PER_SECOND = 10
SCORE_PER_ENEMY = 20
SCORE_PER_BOULDER = 50
BEAST_CHASE_SPEED = 2
ENEMY_SPAWN_RATE = 60
EXP_PER_ENEMY = 10
EXP_PER_BOULDER = 50
LEVEL_UP_EXP = 100
STAGE_DURATION = 300
TITAN_SHARDS_PER_ENEMY = 1
TITAN_SHARDS_PER_SECOND = 0.1
SCREEN_BUFFER = 50  # Pixels for screen buffer

# Frame dimensions
frame_width = 61
frame_height = 58
building_tile_width = 100
building_tile_height = 50
building_scale = 10
scaled_tile_width = building_tile_width * building_scale
scaled_tile_height = building_tile_height * building_scale

# Colors
BLACK = (0, 0, 0)
WHITE = (255, 255, 255)
GRAY = (80, 80, 80)
SKY_BLUE = (135, 206, 235)
RED = (255, 0, 0)
GREEN = (0, 255, 0)
BROWN = (139, 69, 19)

# Audio assets (replace with actual file paths)
GAME_OST = ["game_ost(1).mp3", "game_ost(2).mp3", "game_ost(3).mp3"]
MENU_OST = "menu_ost.mp3"
JUMP_SFX = "jump.mp3"
GRAPPLE_SFX = "grapple.mp3"
HIT_SFX = "hit.mp3"
BEAST_HIT_SFX = "beast_hit.mp3"
ENEMY_HIT_SFX = "beast_hit.mp3"

class Particle:
    def __init__(self, x, y, scale_x, scale_y):
        self.x = x
        self.y = y
        self.vx = random.uniform(-5, 5) * scale_x
        self.vy = random.uniform(-5, 5) * scale_y
        self.life = random.randint(20, 40)
        self.color = GRAY

    def update(self):
        self.x += self.vx
        self.y += self.vy
        self.life -= 1

    def draw(self, surface):
        if self.life > 0:
            pygame.draw.circle(surface, self.color, (int(self.x), int(self.y)), int(3 * self.life / 40))

class Enemy:
    def __init__(self, x, y, scale_x, scale_y):
        self.rect = pygame.Rect(x, y, 30 * scale_x, 30 * scale_y)
        self.health = ENEMY_HEALTH
        self.speed = 2 * scale_x
        self.scale_x = scale_x
        self.scale_y = scale_y

    def update(self, character, game):
        if self.health > 0:
            dx = character.rect.centerx - self.rect.centerx
            dy = character.rect.centery - self.rect.centery
            dist = math.hypot(dx, dy)
            if dist > 0:
                dx, dy = dx / dist, dy / dist
                new_x = self.rect.x + dx * self.speed
                new_y = self.rect.y + dy * self.speed
                # Apply screen buffer
                new_x = max(SCREEN_BUFFER * self.scale_x, min(new_x, game.screen_width - self.rect.width - SCREEN_BUFFER * self.scale_x))
                new_y = max(SCREEN_BUFFER * self.scale_y, min(new_y, game.screen_height - self.rect.height - SCREEN_BUFFER * self.scale_y))
                self.rect.x = new_x
                self.rect.y = new_y
            if self.rect.colliderect(character.rect):
                character.take_damage(5, game)

    def take_damage(self, damage, game):
        self.health -= damage
        game.enemy_hit_sound.play()
        for _ in range(5):
            game.particles.append(Particle(self.rect.centerx, self.rect.centery, self.scale_x, self.scale_y))
        if self.health <= 0:
            if random.random() < 0.5:
                game.character.health = min(game.character.health + 20, PLAYER_HEALTH + game.permanent_upgrades["health"] * 10)
            else:
                game.character.exp += EXP_PER_ENEMY
            game.score += SCORE_PER_ENEMY
            game.titan_shards += TITAN_SHARDS_PER_ENEMY

    def draw(self, surface):
        if self.health > 0:
            pygame.draw.rect(surface, BROWN, self.rect)

class Game:
    def __init__(self):
        self.fullscreen = False
        self.base_width = BASE_SCREEN_WIDTH
        self.base_height = BASE_SCREEN_HEIGHT
        self.screen_info = pygame.display.Info()
        self.fullscreen_width = self.screen_info.current_w
        self.fullscreen_height = self.screen_info.current_h
        self.screen_width = self.base_width
        self.screen_height = self.base_height
        self.scale_x = 1.0
        self.scale_y = 1.0
        self.screen = pygame.display.set_mode((self.screen_width, self.screen_height))
        pygame.display.set_caption("The Rumbling")
        self.clock = pygame.time.Clock()
        
        self.game_state = "menu"
        self.running = True
        self.scroll_speed = 0
        self.bg_offset = 0
        self.world_offset = 0
        self.score = 0
        self.boulders_dodged = 0
        self.game_time = 0
        self.stage_time = 0
        self.stage = 1
        self.enemy_spawn_timer = 0
        self.titan_shards = 0
        self.permanent_upgrades = {"health": 0, "damage": 0, "speed": 0}
        
        self.particles = []
        self.enemies = []
        
        self.jump_sound = pygame.mixer.Sound(JUMP_SFX)
        self.grapple_sound = pygame.mixer.Sound(GRAPPLE_SFX)
        self.hit_sound = pygame.mixer.Sound(HIT_SFX)
        self.beast_hit_sound = pygame.mixer.Sound(BEAST_HIT_SFX)
        self.enemy_hit_sound = pygame.mixer.Sound(ENEMY_HIT_SFX)
        self.play_menu_music()
        
        self.load_assets()
        self.init_game_objects()
    
    def load_assets(self):
        self.spritesheet = pygame.image.load("levi.png").convert_alpha()
        self.beast_titan_sheet = pygame.image.load("attack-on-titan-beast-titan-1.png").convert_alpha()
        self.building_sheet = pygame.image.load("AOT Background buildings.png").convert_alpha()
        self.background_layers = [
            pygame.image.load("shiganshina.png").convert(),
            pygame.image.load("midground.png").convert_alpha(),
            pygame.image.load("foreground.png").convert_alpha()
        ]
        self.loading_screen = pygame.image.load("AOT_loadingscreen.png").convert()
        self.scale_assets()
        self.building_tiles = self.extract_building_tiles()
    
    def scale_assets(self):
        self.scale_x = self.screen_width / self.base_width
        self.scale_y = self.screen_height / self.base_height
        self.background_layers[0] = pygame.transform.scale(
            self.background_layers[0], 
            (int(self.screen_width * 2), int(self.screen_height))
        )
        self.background_layers[1] = pygame.transform.scale(
            self.background_layers[1], 
            (int(self.screen_width * 2), int(self.screen_height))
        )
        self.background_layers[2] = pygame.transform.scale(
            self.background_layers[2], 
            (int(self.screen_width * 2), int(self.screen_height))
        )
        self.loading_screen = pygame.transform.scale(
            self.loading_screen, 
            (int(self.screen_width), int(self.screen_height))
        )
    
    def extract_building_tiles(self):
        tiles = []
        for row in range(2):
            for col in range(5):
                rect = pygame.Rect(col * 100, 50 * row, 100, 50)
                tile = self.building_sheet.subsurface(rect).copy()
                tile = pygame.transform.scale(
                    tile, 
                    (int(100 * building_scale * self.scale_x), int(50 * building_scale * self.scale_y))
                )
                tiles.append(tile)
        return tiles
    
    def init_game_objects(self):
        self.buildings = []
        self.generate_initial_buildings()
        self.character = Character(self.spritesheet, self.scale_x, self.scale_y, self.permanent_upgrades)
        self.beast_titan = None
    
    def generate_initial_buildings(self):
        start_x = -self.screen_width
        end_x = self.screen_width * 2
        x = start_x
        while x < end_x:
            img = random.choice(self.building_tiles)
            tile = BuildingSegment(img, x, self.screen_height, self.scale_x, self.scale_y)
            self.buildings.append(tile)
            x += scaled_tile_width * self.scale_x
    
    def toggle_fullscreen(self):
        self.fullscreen = not self.fullscreen
        if self.fullscreen:
            self.screen_width = self.fullscreen_width
            self.screen_height = self.fullscreen_height
            self.screen = pygame.display.set_mode((self.screen_width, self.screen_height), pygame.FULLSCREEN)
        else:
            self.screen_width = self.base_width
            self.screen_height = self.base_height
            self.screen = pygame.display.set_mode((self.screen_width, self.screen_height))
        self.load_assets()
        self.init_game_objects()
    
    def play_menu_music(self):
        pygame.mixer.music.stop()
        pygame.mixer.music.load(MENU_OST)
        pygame.mixer.music.play(-1)
        self.current_track = MENU_OST
    
    def play_game_music(self):
        pygame.mixer.music.stop()
        track = random.choice(GAME_OST)
        pygame.mixer.music.load(track)
        pygame.mixer.music.play(-1)
        self.current_track = track
    
    def check_music(self):
        if self.game_state == "game" and not pygame.mixer.music.get_busy():
            self.play_game_music()
    
    def show_menu(self):
        self.game_state = "menu"
        self.play_menu_music()
        font = pygame.font.SysFont(None, int(80 * self.scale_y))
        title_font = pygame.font.SysFont(None, int(120 * self.scale_y))
        
        title_text = title_font.render("The Rumbling", True, WHITE)
        start_text = font.render("Press ENTER to Start", True, WHITE)
        fullscreen_text = font.render("Press F for Fullscreen", True, WHITE)
        
        while self.game_state == "menu":
            for event in pygame.event.get():
                if event.type == pygame.QUIT:
                    self.running = False
                    return
                if event.type == pygame.KEYDOWN:
                    if event.key == pygame.K_RETURN:
                        self.game_state = "game"
                        self.score = 0
                        self.boulders_dodged = 0
                        self.game_time = 0
                        self.stage_time = 0
                        self.stage = 1
                        self.titan_shards = 0
                        self.character.health = PLAYER_HEALTH + self.permanent_upgrades["health"] * 10
                        self.init_game_objects()
                        self.play_game_music()
                        return
                    elif event.key == pygame.K_f:
                        self.toggle_fullscreen()
                        font = pygame.font.SysFont(None, int(80 * self.scale_y))
                        title_font = pygame.font.SysFont(None, int(120 * self.scale_y))
                        title_text = title_font.render("The Rumbling", True, WHITE)
                        start_text = font.render("Press ENTER to Start", True, WHITE)
                        fullscreen_text = font.render("Press F for Fullscreen", True, WHITE)
                if event.type == pygame.MOUSEBUTTONDOWN:
                    if event.button == 1:
                        self.game_state = "game"
                        self.score = 0
                        self.boulders_dodged = 0
                        self.game_time = 0
                        self.stage_time = 0
                        self.stage = 1
                        self.titan_shards = 0
                        self.character.health = PLAYER_HEALTH + self.permanent_upgrades["health"] * 10
                        self.init_game_objects()
                        self.play_game_music()
                        return
            self.screen.blit(self.loading_screen, (0, 0))
            self.screen.blit(title_text, (self.screen_width // 2 - title_text.get_width() // 2, int(100 * self.scale_y)))
            self.screen.blit(start_text, (self.screen_width // 2 - start_text.get_width() // 2, int(self.screen_height - 150 * self.scale_y)))
            self.screen.blit(fullscreen_text, (self.screen_width // 2 - fullscreen_text.get_width() // 2, int(self.screen_height - 80 * self.scale_y)))
            pygame.display.flip()
            self.clock.tick(FPS)
    
    def show_pause_menu(self):
        font = pygame.font.SysFont(None, int(50 * self.scale_y))
        title_font = pygame.font.SysFont(None, int(80 * self.scale_y))
        
        pause_text = title_font.render("Paused", True, WHITE)
        resume_text = font.render("Press R to Resume", True, WHITE)
        restart_text = font.render("Press T to Restart", True, WHITE)
        quit_text = font.render("Press Q to Quit", True, WHITE)
        shards_text = font.render(f"Titan Shards: {self.titan_shards}", True, WHITE)
        upgrade_health_text = font.render(f"Upgrade Health (+10): {10 + self.permanent_upgrades['health'] * 5} Shards", True, WHITE)
        upgrade_damage_text = font.render(f"Upgrade Damage (+2): {10 + self.permanent_upgrades['damage'] * 5} Shards", True, WHITE)
        upgrade_speed_text = font.render(f"Upgrade Speed (+0.5): {10 + self.permanent_upgrades['speed'] * 5} Shards", True, WHITE)
        
        while self.game_state == "paused":
            for event in pygame.event.get():
                if event.type == pygame.QUIT:
                    self.running = False
                    return
                if event.type == pygame.KEYDOWN:
                    if event.key == pygame.K_r:
                        self.game_state = "game"
                        self.play_game_music()
                        return
                    elif event.key == pygame.K_t:
                        self.game_state = "game"
                        self.score = 0
                        self.boulders_dodged = 0
                        self.game_time = 0
                        self.stage_time = 0
                        self.stage = 1
                        self.character.health = PLAYER_HEALTH + self.permanent_upgrades["health"] * 10
                        self.init_game_objects()
                        self.play_game_music()
                        return
                    elif event.key == pygame.K_q:
                        self.running = False
                        return
                    elif event.key == pygame.K_1 and self.titan_shards >= 10 + self.permanent_upgrades["health"] * 5:
                        self.titan_shards -= 10 + self.permanent_upgrades["health"] * 5
                        self.permanent_upgrades["health"] += 1
                        self.character.health = min(self.character.health + 10, PLAYER_HEALTH + self.permanent_upgrades["health"] * 10)
                    elif event.key == pygame.K_2 and self.titan_shards >= 10 + self.permanent_upgrades["damage"] * 5:
                        self.titan_shards -= 10 + self.permanent_upgrades["damage"] * 5
                        self.permanent_upgrades["damage"] += 1
                    elif event.key == pygame.K_3 and self.titan_shards >= 10 + self.permanent_upgrades["speed"] * 5:
                        self.titan_shards -= 10 + self.permanent_upgrades["speed"] * 5
                        self.permanent_upgrades["speed"] += 1
                    elif event.key == pygame.K_f:
                        self.toggle_fullscreen()
                        font = pygame.font.SysFont(None, int(50 * self.scale_y))
                        title_font = pygame.font.SysFont(None, int(80 * self.scale_y))
                        pause_text = title_font.render("Paused", True, WHITE)
                        resume_text = font.render("Press R to Resume", True, WHITE)
                        restart_text = font.render("Press T to Restart", True, WHITE)
                        quit_text = font.render("Press Q to Quit", True, WHITE)
                        shards_text = font.render(f"Titan Shards: {self.titan_shards}", True, WHITE)
                        upgrade_health_text = font.render(f"Upgrade Health (+10): {10 + self.permanent_upgrades['health'] * 5} Shards", True, WHITE)
                        upgrade_damage_text = font.render(f"Upgrade Damage (+2): {10 + self.permanent_upgrades['damage'] * 5} Shards", True, WHITE)
                        upgrade_speed_text = font.render(f"Upgrade Speed (+0.5): {10 + self.permanent_upgrades['speed'] * 5} Shards", True, WHITE)
            
            self.screen.fill(BLACK)
            self.screen.blit(pause_text, (self.screen_width // 2 - pause_text.get_width() // 2, int(100 * self.scale_y)))
            self.screen.blit(resume_text, (self.screen_width // 2 - resume_text.get_width() // 2, int(self.screen_height - 250 * self.scale_y)))
            self.screen.blit(restart_text, (self.screen_width // 2 - restart_text.get_width() // 2, int(self.screen_height - 200 * self.scale_y)))
            self.screen.blit(quit_text, (self.screen_width // 2 - quit_text.get_width() // 2, int(self.screen_height - 150 * self.scale_y)))
            self.screen.blit(shards_text, (self.screen_width // 2 - shards_text.get_width() // 2, int(self.screen_height - 100 * self.scale_y)))
            self.screen.blit(upgrade_health_text, (self.screen_width // 2 - upgrade_health_text.get_width() // 2, int(self.screen_height - 50 * self.scale_y)))
            self.screen.blit(upgrade_damage_text, (self.screen_width // 2 - upgrade_damage_text.get_width() // 2, int(self.screen_height - 0 * self.scale_y)))
            self.screen.blit(upgrade_speed_text, (self.screen_width // 2 - upgrade_speed_text.get_width() // 2, int(self.screen_height + 50 * self.scale_y)))
            pygame.display.flip()
            self.clock.tick(FPS)
    
    def show_game_over(self):
        font = pygame.font.SysFont(None, int(80 * self.scale_y))
        title_font = pygame.font.SysFont(None, int(120 * self.scale_y))
        
        game_over_text = title_font.render("Game Over", True, WHITE)
        score_text = font.render(f"Score: {self.score}", True, WHITE)
        restart_text = font.render("Press R to Restart", True, WHITE)
        quit_text = font.render("Press Q to Quit", True, WHITE)
        
        while self.game_state == "game_over":
            for event in pygame.event.get():
                if event.type == pygame.QUIT:
                    self.running = False
                    return
                if event.type == pygame.KEYDOWN:
                    if event.key == pygame.K_r:
                        self.game_state = "game"
                        self.score = 0
                        self.boulders_dodged = 0
                        self.game_time = 0
                        self.stage_time = 0
                        self.stage = 1
                        self.character.health = PLAYER_HEALTH + self.permanent_upgrades["health"] * 10
                        self.init_game_objects()
                        self.play_game_music()
                        return
                    elif event.key == pygame.K_q:
                        self.running = False
                        return
                    elif event.key == pygame.K_f:
                        self.toggle_fullscreen()
                        font = pygame.font.SysFont(None, int(80 * self.scale_y))
                        title_font = pygame.font.SysFont(None, int(120 * self.scale_y))
                        game_over_text = title_font.render("Game Over", True, WHITE)
                        score_text = font.render(f"Score: {self.score}", True, WHITE)
                        restart_text = font.render("Press R to Restart", True, WHITE)
                        quit_text = font.render("Press Q to Quit", True, WHITE)
            
            self.screen.fill(BLACK)
            self.screen.blit(game_over_text, (self.screen_width // 2 - game_over_text.get_width() // 2, int(100 * self.scale_y)))
            self.screen.blit(score_text, (self.screen_width // 2 - score_text.get_width() // 2, int(self.screen_height - 200 * self.scale_y)))
            self.screen.blit(restart_text, (self.screen_width // 2 - restart_text.get_width() // 2, int(self.screen_height - 150 * self.scale_y)))
            self.screen.blit(quit_text, (self.screen_width // 2 - quit_text.get_width() // 2, int(self.screen_height - 100 * self.scale_y)))
            pygame.display.flip()
            self.clock.tick(FPS)
    
    def show_victory(self):
        font = pygame.font.SysFont(None, int(80 * self.scale_y))
        title_font = pygame.font.SysFont(None, int(120 * self.scale_y))
        
        victory_text = title_font.render("Stage Cleared!", True, WHITE)
        score_text = font.render(f"Score: {self.score}", True, WHITE)
        next_stage_text = font.render("Press R to Next Stage", True, WHITE)
        quit_text = font.render("Press Q to Quit", True, WHITE)
        
        while self.game_state == "victory":
            for event in pygame.event.get():
                if event.type == pygame.QUIT:
                    self.running = False
                    return
                if event.type == pygame.KEYDOWN:
                    if event.key == pygame.K_r:
                        self.game_state = "game"
                        self.stage += 1
                        self.stage_time = 0
                        self.character.health = PLAYER_HEALTH + self.permanent_upgrades["health"] * 10
                        self.init_game_objects()
                        self.play_game_music()
                        return
                    elif event.key == pygame.K_q:
                        self.running = False
                        return
                    elif event.key == pygame.K_f:
                        self.toggle_fullscreen()
                        font = pygame.font.SysFont(None, int(80 * self.scale_y))
                        title_font = pygame.font.SysFont(None, int(120 * self.scale_y))
                        victory_text = title_font.render("Stage Cleared!", True, WHITE)
                        score_text = font.render(f"Score: {self.score}", True, WHITE)
                        next_stage_text = font.render("Press R to Next Stage", True, WHITE)
                        quit_text = font.render("Press Q to Quit", True, WHITE)
            
            self.screen.fill(BLACK)
            self.screen.blit(victory_text, (self.screen_width // 2 - victory_text.get_width() // 2, int(100 * self.scale_y)))
            self.screen.blit(score_text, (self.screen_width // 2 - score_text.get_width() // 2, int(self.screen_height - 200 * self.scale_y)))
            self.screen.blit(next_stage_text, (self.screen_width // 2 - next_stage_text.get_width() // 2, int(self.screen_height - 150 * self.scale_y)))
            self.screen.blit(quit_text, (self.screen_width // 2 - quit_text.get_width() // 2, int(self.screen_height - 100 * self.scale_y)))
            pygame.display.flip()
            self.clock.tick(FPS)
    
    def show_level_up(self):
        font = pygame.font.SysFont(None, int(50 * self.scale_y))
        title_font = pygame.font.SysFont(None, int(80 * self.scale_y))
        
        level_up_text = title_font.render("Level Up!", True, WHITE)
        skill1_text = font.render("1: Faster Attack (+10%)", True, WHITE)
        skill2_text = font.render("2: AOE Slash (+10%)", True, WHITE)
        skill3_text = font.render("3: Speed Boost (+10%)", True, WHITE)
        
        while self.game_state == "level_up":
            for event in pygame.event.get():
                if event.type == pygame.QUIT:
                    self.running = False
                    return
                if event.type == pygame.KEYDOWN:
                    if event.key == pygame.K_1:
                        self.character.attack_speed *= 0.9
                        self.game_state = "game"
                        self.play_game_music()
                        return
                    elif event.key == pygame.K_2:
                        self.character.aoe_damage += 0.1
                        self.game_state = "game"
                        self.play_game_music()
                        return
                    elif event.key == pygame.K_3:
                        self.character.move_speed += 0.5 * self.scale_x
                        self.game_state = "game"
                        self.play_game_music()
                        return
            
            self.screen.fill(BLACK)
            self.screen.blit(level_up_text, (self.screen_width // 2 - level_up_text.get_width() // 2, int(100 * self.scale_y)))
            self.screen.blit(skill1_text, (self.screen_width // 2 - skill1_text.get_width() // 2, int(self.screen_height - 200 * self.scale_y)))
            self.screen.blit(skill2_text, (self.screen_width // 2 - skill2_text.get_width() // 2, int(self.screen_height - 150 * self.scale_y)))
            self.screen.blit(skill3_text, (self.screen_width // 2 - skill3_text.get_width() // 2, int(self.screen_height - 100 * self.scale_y)))
            pygame.display.flip()
            self.clock.tick(FPS)
    
    def get_surface_below(self, rect):
        for segment in self.buildings:
            if rect.colliderect(segment.roof_rect):
                return segment
        return None
    
    def handle_events(self):
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                self.running = False
            if event.type == pygame.KEYDOWN:
                if event.key == pygame.K_f:
                    self.toggle_fullscreen()
                elif event.key == pygame.K_ESCAPE or event.key == pygame.K_p:
                    if self.game_state == "game":
                        self.game_state = "paused"
                        pygame.mixer.music.pause()
            if event.type == pygame.MOUSEBUTTONDOWN:
                if event.button == 1 and self.game_state == "game":
                    self.character.state = self.character.GRAPPLE
                    self.grapple_sound.play()
    
    def update(self):
        keys = pygame.key.get_pressed()
        self.scroll_speed = 0
        self.game_time += 1 / FPS
        self.stage_time += 1 / FPS
        self.titan_shards += TITAN_SHARDS_PER_SECOND / FPS
        self.score = int(self.game_time * SCORE_PER_SECOND + self.boulders_dodged * SCORE_PER_BOULDER + len(self.enemies) * SCORE_PER_ENEMY)
        
        # Spawn enemies
        self.enemy_spawn_timer -= 1
        if self.enemy_spawn_timer <= 0 and self.beast_titan is None:
            spawn_x = random.choice([0, self.screen_width]) + random.randint(-100, 100) * self.scale_x
            spawn_y = self.screen_height - scaled_tile_height * self.scale_y
            self.enemies.append(Enemy(spawn_x, spawn_y, self.scale_x, self.scale_y))
            self.enemy_spawn_timer = max(10, ENEMY_SPAWN_RATE - self.stage * 5)
        
        # Update character
        move_speed = PLAYER_SPEED + self.permanent_upgrades["speed"] * 0.5
        if self.character.is_grappling():
            direction = 1 if self.character.facing_right else -1
            new_x = self.character.rect.x + GRAPPLE_SPEED * self.scale_x * direction
            new_x = max(SCREEN_BUFFER * self.scale_x, min(new_x, self.screen_width - self.character.rect.width - SCREEN_BUFFER * self.scale_x))
            if new_x == SCREEN_BUFFER * self.scale_x or new_x == self.screen_width - self.character.rect.width - SCREEN_BUFFER * self.scale_x:
                self.scroll_speed = GRAPPLE_SPEED * self.scale_x * direction
            else:
                self.character.rect.x = new_x
        
        if keys[pygame.K_SPACE] and self.character.on_ground:
            self.jump_sound.play()
        
        self.character.apply_gravity()
        self.character.update(keys, self.beast_titan, self.enemies, self)
        
        # Check for landing
        surface = self.get_surface_below(self.character.rect)
        if surface and self.character.vel_y >= 0:
            self.character.land_on(surface)
        else:
            self.character.on_ground = False
        
        # Handle movement with screen buffer
        if keys[pygame.K_a]:
            new_x = self.character.rect.x - move_speed * self.scale_x
            new_x = max(SCREEN_BUFFER * self.scale_x, new_x)
            if new_x == SCREEN_BUFFER * self.scale_x:
                self.scroll_speed = -move_speed * self.scale_x
            else:
                self.character.rect.x = new_x
            self.character.facing_right = False
        if keys[pygame.K_d]:
            new_x = self.character.rect.x + move_speed * self.scale_x
            new_x = min(new_x, self.screen_width - self.character.rect.width - SCREEN_BUFFER * self.scale_x)
            if new_x == self.screen_width - self.character.rect.width - SCREEN_BUFFER * self.scale_x:
                self.scroll_speed = move_speed * self.scale_x
            else:
                self.character.rect.x = new_x
            self.character.facing_right = True
        
        # Update beast titan
        if self.beast_titan:
            self.beast_titan.update(self.character, self)
        
        # Update enemies
        new_enemies = []
        for enemy in self.enemies:
            enemy.update(self.character, self)
            if enemy.health > 0:
                new_enemies.append(enemy)
        self.enemies = new_enemies
        
        # Update particles
        new_particles = []
        for particle in self.particles:
            particle.update()
            if particle.life > 0:
                new_particles.append(particle)
        self.particles = new_particles
        
        # Apply scrolling
        if self.scroll_speed != 0:
            self.apply_scrolling()
        
        # Manage buildings
        self.manage_buildings()
        
        # Check stage progression
        if self.stage_time >= STAGE_DURATION and self.beast_titan is None:
            self.beast_titan = BeastTitan(self.beast_titan_sheet, self.scale_x, self.scale_y)
        
        # Check game over or victory
        if self.character.health <= 0:
            self.game_state = "game_over"
            pygame.mixer.music.stop()
        elif self.beast_titan and self.beast_titan.health <= 0:
            self.game_state = "victory"
            pygame.mixer.music.stop()
    
    def manage_buildings(self):
        self.buildings = [b for b in self.buildings if -self.screen_width * 2 < b.x < self.screen_width * 2]
        leftmost = min(b.x for b in self.buildings)
        rightmost = max(b.x for b in self.buildings)
        while leftmost > -self.screen_width:
            new_x = leftmost - scaled_tile_width * self.scale_x
            new_segment = BuildingSegment(random.choice(self.building_tiles), new_x, self.screen_height, self.scale_x, self.scale_y)
            self.buildings.append(new_segment)
            leftmost = new_x
        while rightmost < self.screen_width * 2:
            new_x = rightmost + scaled_tile_width * self.scale_x
            new_segment = BuildingSegment(random.choice(self.building_tiles), new_x, self.screen_height, self.scale_x, self.scale_y)
            self.buildings.append(new_segment)
            rightmost = new_x
    
    def apply_scrolling(self):
        self.world_offset += self.scroll_speed
        for segment in self.buildings:
            segment.update(self.scroll_speed)
        if self.beast_titan:
            self.beast_titan.rect.x -= self.scroll_speed
            for b in self.beast_titan.boulders:
                b[0] -= self.scroll_speed
        for enemy in self.enemies:
            enemy.rect.x -= self.scroll_speed
    
    def draw(self):
        for i, layer in enumerate(self.background_layers):
            parallax = PARALLAX_FACTOR * (1 - i * 0.2)
            bg_x = -self.bg_offset * parallax % layer.get_width()
            self.screen.blit(layer, (bg_x - layer.get_width(), 0))
            self.screen.blit(layer, (bg_x, 0))
        
        for segment in self.buildings:
            segment.draw(self.screen)
        
        for enemy in self.enemies:
            enemy.draw(self.screen)
        
        if self.character.is_grappling():
            target_x = self.character.rect.centerx + (100 * self.scale_x if self.character.facing_right else -100 * self.scale_x)
            target_y = self.character.rect.centery - 50 * self.scale_y
            pygame.draw.line(self.screen, WHITE, self.character.rect.center, (target_x, target_y), int(2 * self.scale_x))
            for _ in range(5):
                self.particles.append(Particle(target_x, target_y, self.scale_x, self.scale_y))
        
        if self.beast_titan:
            self.beast_titan.draw(self.screen)
        
        self.screen.blit(self.character.image, self.character.rect)
        
        for particle in self.particles:
            particle.draw(self.screen)
        
        health_width = int(200 * self.scale_x * (self.character.health / (PLAYER_HEALTH + self.permanent_upgrades["health"] * 10)))
        pygame.draw.rect(self.screen, RED, (int(10 * self.scale_x), int(10 * self.scale_y), int(200 * self.scale_x), int(20 * self.scale_y)))
        pygame.draw.rect(self.screen, GREEN, (int(10 * self.scale_x), int(10 * self.scale_y), health_width, int(20 * self.scale_y)))
        
        if self.beast_titan:
            beast_health_width = int(200 * self.scale_x * (self.beast_titan.health / BEAST_HEALTH))
            pygame.draw.rect(self.screen, RED, (int(10 * self.scale_x), int(40 * self.scale_y), int(200 * self.scale_x), int(20 * self.scale_y)))
            pygame.draw.rect(self.screen, GREEN, (int(10 * self.scale_x), int(40 * self.scale_y), beast_health_width, int(20 * self.scale_y)))
        
        font = pygame.font.SysFont(None, int(40 * self.scale_y))
        score_text = font.render(f"Score: {self.score}", True, WHITE)
        stage_text = font.render(f"Stage: {self.stage}", True, WHITE)
        exp_text = font.render(f"EXP: {self.character.exp}/{LEVEL_UP_EXP}", True, WHITE)
        self.screen.blit(score_text, (self.screen_width - score_text.get_width() - int(10 * self.scale_x), int(10 * self.scale_y)))
        self.screen.blit(stage_text, (self.screen_width - stage_text.get_width() - int(10 * self.scale_x), int(50 * self.scale_y)))
        self.screen.blit(exp_text, (self.screen_width - exp_text.get_width() - int(10 * self.scale_x), int(90 * self.scale_y)))
        
        pygame.display.flip()
    
    def run(self):
        self.screen.blit(self.loading_screen, (0, 0))
        pygame.display.flip()
        pygame.time.wait(1000)
        
        while self.running:
            if self.game_state == "menu":
                self.show_menu()
            elif self.game_state == "game":
                self.handle_events()
                self.update()
                self.draw()
                self.check_music()
            elif self.game_state == "paused":
                self.show_pause_menu()
            elif self.game_state == "game_over":
                self.show_game_over()
            elif self.game_state == "victory":
                self.show_victory()
            elif self.game_state == "level_up":
                self.show_level_up()
            self.clock.tick(FPS)
        
        pygame.mixer.music.stop()
        pygame.quit()

class BuildingSegment:
    def __init__(self, image, x, screen_height, scale_x, scale_y):
        self.image = image
        self.x = x
        self.y = screen_height - (50 * building_scale * scale_y)
        self.rect = pygame.Rect(self.x, self.y, 100 * building_scale * scale_x, 50 * building_scale * scale_y)
        self.roof_height = 100 * scale_y
        self.roof_rect = pygame.Rect(
            self.x, self.y + ((50 * building_scale * scale_y) - self.roof_height),
            100 * building_scale * scale_x, self.roof_height
        )

    def update(self, speed):
        self.x -= speed
        self.rect.x = self.x
        self.roof_rect.x = self.x

    def draw(self, surface):
        surface.blit(self.image, (self.x, self.y))

class Character(pygame.sprite.Sprite):
    def __init__(self, spritesheet, scale_x, scale_y, upgrades):
        super().__init__()
        self.spritesheet = spritesheet
        self.scale_x = scale_x
        self.scale_y = scale_y
        self.image = pygame.Surface((frame_width * scale_x, frame_height * scale_y), pygame.SRCALPHA)
        self.rect = self.image.get_rect()
        self.rect.midbottom = (200 * scale_x, BASE_SCREEN_HEIGHT - scaled_tile_height * scale_y)

        self.IDLE = 0
        self.ATTACK = 1
        self.GRAPPLE = 2
        self.LAND = 3

        self.state = self.IDLE
        self.facing_right = True
        self.frame = 0
        self.timer = 0
        self.attack_timer = 0
        self.attack_speed = 30
        self.move_speed = PLAYER_SPEED
        self.health = PLAYER_HEALTH + upgrades["health"] * 10
        self.base_damage = ATTACK_DAMAGE + upgrades["damage"] * 2
        self.grapple_hit_timer = 0
        self.exp = 0
        self.level = 1
        self.aoe_damage = 0

        self.vel_y = 0
        self.jump_power = JUMP_POWER * scale_y
        self.on_ground = False

        self.frame_map = {
            self.IDLE: [(i, 0) for i in range(4)],
            self.ATTACK: [(i + 4, 0) for i in range(2)],
            self.GRAPPLE: [(i % 6, 1 + i // 6) for i in range(12)],
            self.LAND: [(5, 2)]
        }

        self.update_image()

    def update_image(self):
        fx, fy = self.frame_map[self.state][self.frame]
        self.image = self.spritesheet.subsurface(
            fx * frame_width, fy * frame_height, frame_width, frame_height
        )
        self.image = pygame.transform.scale(
            self.image, 
            (int(frame_width * 2 * self.scale_x), int(frame_height * 2 * self.scale_y))
        )
        if not self.facing_right:
            self.image = pygame.transform.flip(self.image, True, False)

    def update(self, keys, beast_titan, enemies, game):
        self.grapple_hit_timer -= 1 if self.grapple_hit_timer > 0 else 0

        if keys[pygame.K_SPACE] and self.on_ground:
            self.vel_y = self.jump_power
            self.on_ground = False

        self.attack_timer -= 1
        if self.attack_timer <= 0:
            self.state = self.ATTACK
            attack_range = 100 * self.scale_x
            for enemy in enemies:
                if math.hypot(self.rect.centerx - enemy.rect.centerx, self.rect.centery - enemy.rect.centery) < attack_range:
                    enemy.take_damage(self.base_damage, game)
                    if self.aoe_damage > 0:
                        for other_enemy in enemies:
                            if other_enemy != enemy and math.hypot(self.rect.centerx - other_enemy.rect.centerx, self.rect.centery - other_enemy.rect.centery) < attack_range * 1.5:
                                other_enemy.take_damage(self.base_damage * self.aoe_damage, game)
            self.attack_timer = self.attack_speed
        elif self.state != self.GRAPPLE:
            self.state = self.IDLE

        if self.is_grappling() and self.grapple_hit_timer <= 0:
            target_x = self.rect.centerx + (100 * self.scale_x if self.facing_right else -100 * self.scale_x)
            target_y = self.rect.centery - 50 * self.scale_y
            grapple_range = 150 * self.scale_x
            if beast_titan and math.hypot(target_x - beast_titan.rect.centerx, target_y - beast_titan.rect.centery) < grapple_range:
                beast_titan.take_damage(GRAPPLE_DAMAGE, game)
                self.grapple_hit_timer = GRAPPLE_HIT_COOLDOWN
            for enemy in enemies:
                if math.hypot(target_x - enemy.rect.centerx, target_y - enemy.rect.centery) < grapple_range:
                    enemy.take_damage(GRAPPLE_DAMAGE, game)
                    self.grapple_hit_timer = GRAPPLE_HIT_COOLDOWN

        self.timer += 1
        if self.timer >= 10:
            self.timer = 0
            self.frame += 1
            if self.frame >= len(self.frame_map[self.state]):
                self.frame = 0
                if self.state in [self.ATTACK, self.GRAPPLE]:
                    self.state = self.IDLE
            self.update_image()

        if self.exp >= LEVEL_UP_EXP:
            self.exp -= LEVEL_UP_EXP
            self.level += 1
            game.game_state = "level_up"
            pygame.mixer.music.pause()

    def apply_gravity(self):
        self.vel_y += GRAVITY * self.scale_y
        self.rect.y = min(self.rect.y + self.vel_y, BASE_SCREEN_HEIGHT - self.rect.height - SCREEN_BUFFER * self.scale_y)

    def land_on(self, surface):
        self.rect.bottom = surface.roof_rect.top
        self.vel_y = 0
        self.on_ground = True

    def is_grappling(self):
        return self.state == self.GRAPPLE

    def take_damage(self, damage, game):
        self.health -= damage
        game.hit_sound.play()
        for _ in range(10):
            game.particles.append(Particle(self.rect.centerx, self.rect.centery, self.scale_x, self.scale_y))
        if self.health < 0:
            self.health = 0

class BeastTitan:
    def __init__(self, spritesheet, scale_x, scale_y):
        self.frames = []
        self.scale_x = scale_x
        self.scale_y = scale_y
        scale_factor = 10
        
        # Fix frame extraction from spritesheet
        frame_width = 133
        frame_height = 123
        num_frames = 13
        
        for i in range(num_frames):
            # Ensure we're extracting frames correctly from the spritesheet
            frame = spritesheet.subsurface(i * frame_width, 0, frame_width, frame_height)
            frame = pygame.transform.scale(
                frame, 
                (int(frame_width * scale_factor * scale_x), int(frame_height * scale_factor * scale_y))
            )
            self.frames.append(frame)
            
        self.image = self.frames[0]
        self.rect = self.image.get_rect(bottom=800 + 140 * scale_y)
        self.rect.x = 1200 * scale_x
        self.frame_index = 0
        self.timer = 0
        self.boulder_cooldown = 120
        self.boulders = []
        self.boulder_speed = 10 * scale_x
        self.health = BEAST_HEALTH
        self.chase_speed = BEAST_CHASE_SPEED * scale_x

    def update(self, character, game):
        self.timer += 1
        if self.timer >= 8:
            self.timer = 0
            self.frame_index = (self.frame_index + 1) % len(self.frames)
            self.image = self.frames[self.frame_index]

        self.facing_right = character.rect.centerx < self.rect.centerx
        self.image = self.frames[self.frame_index]
        if not self.facing_right:
            self.image = pygame.transform.flip(self.image, True, False)

        if self.health > 0:
            direction = -1 if self.facing_right else 1
            new_x = self.rect.x + self.chase_speed * direction
            new_x = max(SCREEN_BUFFER * self.scale_x, min(new_x, game.screen_width - self.rect.width - SCREEN_BUFFER * self.scale_x))
            self.rect.x = new_x

        self.boulder_cooldown -= 1
        if self.boulder_cooldown <= 0 and self.health > 0:
            self.throw_boulder(character.rect.centery)
            self.boulder_cooldown = 120

        player_rect = character.rect
        new_boulders = []
        for boulder in self.boulders:
            boulder[0] -= self.boulder_speed
            boulder_rect = pygame.Rect(boulder[0] - 20 * self.scale_x, boulder[1] - 20 * self.scale_y, 40 * self.scale_x, 40 * self.scale_y)
            if boulder_rect.colliderect(player_rect):
                character.take_damage(BOULDER_DAMAGE, game)
            else:
                if boulder[0] > -40 * self.scale_x:
                    new_boulders.append(boulder)
                else:
                    game.boulders_dodged += 1
                    game.character.exp += EXP_PER_BOULDER
                    game.titan_shards += TITAN_SHARDS_PER_ENEMY
        self.boulders = new_boulders

    def throw_boulder(self, target_y):
        boulder = [self.rect.centerx, self.rect.centery, target_y]
        self.boulders.append(boulder)

    def take_damage(self, damage, game):
        self.health -= damage
        game.beast_hit_sound.play()
        for _ in range(10):
            game.particles.append(Particle(self.rect.centerx, self.rect.centery, self.scale_x, self.scale_y))
        if self.health < 0:
            self.health = 0

    def draw(self, surface):
        if self.health > 0:
            surface.blit(self.image, self.rect)
            for b in self.boulders:
                b[1] += (b[2] - b[1]) * 0.05
                pygame.draw.circle(surface, (80, 80, 80), (int(b[0]), int(b[1])), int(20 * self.scale_x))

if __name__ == "__main__":
    game = Game()
    game.run()
    pygame.quit()
