#!/usr/bin/env python3
"""
Test script to verify the AOT game starts and closes cleanly.
This script will start the game and automatically close it after a few seconds.
"""

import subprocess
import time
import sys
import os

def test_game_startup():
    """Test that the game starts without errors."""
    print("Testing AOT Survive game startup...")
    
    try:
        # Start the game process
        process = subprocess.Popen([sys.executable, "tester.py"], 
                                 stdout=subprocess.PIPE, 
                                 stderr=subprocess.PIPE,
                                 text=True)
        
        # Wait a few seconds for the game to initialize
        time.sleep(3)
        
        # Check if process is still running (good sign)
        if process.poll() is None:
            print("✅ Game started successfully!")
            
            # Terminate the process gracefully
            process.terminate()
            
            # Wait for clean shutdown
            try:
                stdout, stderr = process.communicate(timeout=5)
                
                if process.returncode == 0 or process.returncode == -15:  # 0 = clean exit, -15 = SIGTERM
                    print("✅ Game closed cleanly!")
                    return True
                else:
                    print(f"⚠️  Game exited with code: {process.returncode}")
                    if stderr:
                        print(f"Errors: {stderr}")
                    return False
                    
            except subprocess.TimeoutExpired:
                print("⚠️  Game didn't close gracefully, forcing termination...")
                process.kill()
                return False
                
        else:
            # Process exited immediately - check for errors
            stdout, stderr = process.communicate()
            print(f"❌ Game exited immediately with code: {process.returncode}")
            if stderr:
                print(f"Errors: {stderr}")
            return False
            
    except FileNotFoundError:
        print("❌ Could not find tester.py - make sure you're in the correct directory")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

def check_assets():
    """Check if all required assets are present."""
    print("\nChecking game assets...")
    
    required_files = [
        "tester.py",
        "AOT_loadingscreen.png",
        "levi.png",
        "mikasa.png",
        "attack-on-titan-beast-titan-1.png",
        "titan.png",
        "AOT Background buildings.png",
        "midground.png",
        "foreground.png",
        "shiganshina.png",
        "menu_ost.mp3",
        "game_ost(1).mp3",
        "game_ost(2).mp3",
        "game_ost(3).mp3",
        "grapple.mp3",
        "hit.mp3",
        "beast_hit.mp3",
        "jump.mp3"
    ]
    
    missing_files = []
    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)
    
    if missing_files:
        print("❌ Missing required files:")
        for file in missing_files:
            print(f"   - {file}")
        return False
    else:
        print("✅ All required assets found!")
        return True

def main():
    """Run all tests."""
    print("=" * 50)
    print("AOT Survive Game Test Suite")
    print("=" * 50)
    
    # Check assets first
    assets_ok = check_assets()
    
    if not assets_ok:
        print("\n❌ Cannot run game tests - missing assets")
        return False
    
    # Test game startup
    game_ok = test_game_startup()
    
    print("\n" + "=" * 50)
    if assets_ok and game_ok:
        print("🎉 All tests passed! Game is ready to play.")
        print("\nTo start the game manually, run:")
        print("   python tester.py")
    else:
        print("❌ Some tests failed. Check the output above for details.")
    print("=" * 50)
    
    return assets_ok and game_ok

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
