import pygame
import random
import os
import sys
import math
import asyncio
import platform

# --- Pygame Initialization ---
pygame.init()
pygame.mixer.init(frequency=22050, size=-16, channels=2, buffer=512)

# --- Game Constants ---
SCREEN_WIDTH = 1000
SCREEN_HEIGHT = 700
FPS = 60
MENU_FPS = 30

# Set up the display
screen = pygame.display.set_mode((SCREEN_WIDTH, SCREEN_HEIGHT))
pygame.display.set_caption("Attack on Titan: Survive")

# Colors
WHITE = (255, 255, 255)
BLACK = (0, 0, 0)
RED = (255, 0, 0)
GREEN = (0, 255, 0)
BLUE = (0, 0, 255)
YELLOW = (255, 255, 0)
GRAY = (150, 150, 150)
DARK_RED = (139, 0, 0)
GOLD = (255, 215, 0)

# Game World Dimensions
WORLD_WIDTH = 6000
WORLD_HEIGHT = 6000

# --- Asset Paths ---
ASSET_DIR = ""

IMAGE_PATHS = {
    'loading_screen': os.path.join(ASSET_DIR, 'AOT_loadingscreen.png'),
    'player_sprite': os.path.join(ASSET_DIR, 'levi.png'),
    'mikasa_sprite': os.path.join(ASSET_DIR, 'mikasa.png'),
    'beast_titan_sprite': os.path.join(ASSET_DIR, 'attack-on-titan-beast-titan-1.png'),
    'titan_sprite': os.path.join(ASSET_DIR, 'titan.png'),
    'bg_buildings': os.path.join(ASSET_DIR, 'AOT Background buildings.png'),
    'midground': os.path.join(ASSET_DIR, 'midground.png'),
    'foreground': os.path.join(ASSET_DIR, 'foreground.png'),
    'shiganshina': os.path.join(ASSET_DIR, 'ground.png'),
    'side_building1': os.path.join(ASSET_DIR, 'side_building1.png'),  # New side-view building
    'side_building2': os.path.join(ASSET_DIR, 'side_building2.png'),  # New side-view building
}

SOUND_PATHS = {
    'menu_music': os.path.join(ASSET_DIR, 'menu_ost.mp3'),
    'game_music_1': os.path.join(ASSET_DIR, 'game_ost(1).mp3'),
    'game_music_2': os.path.join(ASSET_DIR, 'game_ost(2).mp3'),
    'game_music_3': os.path.join(ASSET_DIR, 'game_ost(3).mp3'),
    'grapple_sound': os.path.join(ASSET_DIR, 'grapple.mp3'),
    'hit_sound': os.path.join(ASSET_DIR, 'hit.mp3'),
    'beast_hit_sound': os.path.join(ASSET_DIR, 'beast_hit.mp3'),
    'jump_sound': os.path.join(ASSET_DIR, 'jump.mp3')
}

# --- Asset Loading ---
def load_assets():
    """Loads all game images and sounds."""
    assets = {}

    # Load images
    for key, path in IMAGE_PATHS.items():
        try:
            image = pygame.image.load(path).convert_alpha()
            assets[key] = image
            print(f"Loaded image: {path}")
        except pygame.error as e:
            print(f"Error loading image {path}: {e}")
            placeholder_surface = pygame.Surface((100, 100))
            placeholder_surface.fill(GRAY)
            font = pygame.font.Font(None, 20)
            text_surf = font.render(key, True, BLACK)
            text_rect = text_surf.get_rect(center=(50, 50))
            placeholder_surface.blit(text_surf, text_rect)
            assets[key] = placeholder_surface

    # Load sounds
    for key, path in SOUND_PATHS.items():
        try:
            if 'music' in key:
                assets[key] = path
                print(f"Registered music: {path}")
            else:
                sound = pygame.mixer.Sound(path)
                assets[key] = sound
                print(f"Loaded sound: {path}")
        except pygame.error as e:
            print(f"Error loading sound {path}: {e}")
            assets[key] = None

    return assets

# --- Music Management ---
current_music = None
music_volume = 0.7

def play_music(music_path, loops=-1):
    """Play background music."""
    global current_music
    if music_path and current_music != music_path:
        try:
            pygame.mixer.music.load(music_path)
            pygame.mixer.music.set_volume(music_volume)
            pygame.mixer.music.play(loops)
            current_music = music_path
        except pygame.error as e:
            print(f"Error playing music {music_path}: {e}")

def stop_music():
    """Stop background music."""
    global current_music
    pygame.mixer.music.stop()
    current_music = None

# --- Game State ---
game_state = 'MENU'
assets = {}
camera_x, camera_y = 0, 0
selected_character = 'levi'
game_running = True

# Game statistics
total_kills = 0
total_score = 0
wave_number = 1
titans_killed_this_wave = 0
titans_needed_for_next_wave = 5

# --- Sprite Sheet Data ---
PLAYER SPRITE_DATA = {
    'frame_width': 61,
    'frame_height': 58,
    'scale': 2.0,
    'animations': {
        'idle': [(0, 0), (0, 1), (0, 2), (0, 3), (0, 4)],
        'walk_right': [(0, 3), (0, 4)],
        'walk_left': [(0, 3), (0, 4)],
        'attack': [(0, 4), (0, 5), (1, 0), (1, 1), (1, 2), (1, 3), (1, 4), (1, 5), (2, 0), (2, 1), (2, 2), (2, 3), (2, 4), (2, 5)]
    }
}

TITAN_SPRITE_DATA = {
    'frame_width': 96,
    'frame_height': 96,
    'scale': 2.0,
    'animations': {
        'idle': [(0, 0), (0, 1), (0, 2)],
        'walk': [(0, 0), (0, 1), (0, 2)],
        'attack': [(0, 3), (0, 4), (0, 5), (0, 6), (0, 7), (0, 8), (0, 9)]
    }
}

# --- Building Data ---
BUILDING_DATA = {
    'side_building1': {
        'width': 200,
        'height': 400,
        'scale': 1.5,
        'parallax_factor': 0.6
    },
    'side_building2': {
        'width': 150,
        'height': 300,
        'scale': 1.2,
        'parallax_factor': 0.7
    }
}

# --- Particle System ---
class Particle:
    def __init__(self, x, y, color, velocity_x, velocity_y, lifetime):
        self.x = x
        self.y = y
        self.color = color
        self.velocity_x = velocity_x
        self.velocity_y = velocity_y
        self.lifetime = lifetime
        self.max_lifetime = lifetime

    def update(self, dt):
        self.x += self.velocity_x * dt / 1000
        self.y += self.velocity_y * dt / 1000
        self.lifetime -= dt
        return self.lifetime > 0

    def draw(self, screen, camera_x, camera_y):
        alpha = int(255 * (self.lifetime / self.max_lifetime))
        color_with_alpha = (*self.color[:3], alpha)
        particle_surface = pygame.Surface((4, 4), pygame.SRCALPHA)
        particle_surface.fill(color_with_alpha)
        screen.blit(particle_surface, (self.x - camera_x, self.y - camera_y))

particles = []

def create_hit_particles(x, y, color=RED):
    for _ in range(8):
        velocity_x = random.uniform(-100, 100)
        velocity_y = random.uniform(-100, 100)
        particles.append(Particle(x, y, color, velocity_x, velocity_y, 500))

def create_grapple_particles(x, y):
    for _ in range(3):
        velocity_x = random.uniform(-50, 50)
        velocity_y = random.uniform(-50, 50)
        particles.append(Particle(x, y, BLUE, velocity_x, velocity_y, 300))

def update_particles(dt):
    global particles
    particles = [p for p in particles if p.update(dt)]

def draw_particles(screen, camera_x, camera_y):
    for particle in particles:
        particle.draw(screen, camera_x, camera_y)

# --- Game Entities ---
class Entity(pygame.sprite.Sprite):
    def __init__(self, x, y, width, height, sprite_img, sprite_data=None):
        super().__init__()
        self.x = x
        self.y = y
        self.original_width = width
        self.original_height = height
        self.sprite_sheet = sprite_img
        self.sprite_data = sprite_data
        self.current_animation = 'idle' if sprite_data else None
        self.frame_index = 0
        self.animation_timer = 0
        self.frame_delay = 100

        # Scale
        scale = sprite_data['scale'] if sprite_data else 1.0
        self.width = int(self.original_width * scale)
        self.height = int(self.original_height * scale)
        self.image = pygame.Surface((self.width, self.height), pygame.SRCALPHA)
        self.rect = self.image.get_rect(topleft=(self.x, self.y))
        if not sprite_data:
            self.image = pygame.transform.scale(sprite_img, (self.width, self.height)) if sprite_img else self.image

    def get_frame(self):
        if not self.sprite_data:
            return self.image
        try:
            if (self.current_animation not in self.sprite_data['animations'] or
                not self.sprite_data['animations'][self.current_animation]):
                self.current_animation = 'idle'
                self.frame_index = 0
            frames = self.sprite_data['animations'][self.current_animation]
            if not frames:
                return pygame.Surface((self.width, self.height), pygame.SRCALPHA)
            if self.frame_index >= len(frames):
                self.frame_index = 0
            row, col = frames[self.frame_index]
            frame_rect = pygame.Rect(
                col * self.sprite_data['frame_width'],
                row * self.sprite_data['frame_height'],
                self.sprite_data['frame_width'],
                self.sprite_data['frame_height']
            )
            frame_surface = pygame.Surface((self.sprite_data['frame_width'], self.sprite_data['frame_height']), pygame.SRCALPHA)
            if (frame_rect.right <= self.sprite_sheet.get_width() and
                frame_rect.bottom <= self.sprite_sheet.get_height()):
                frame_surface.blit(self.sprite_sheet, (0, 0), frame_rect)
            else:
                fallback_rect = pygame.Rect(0, 0, self.sprite_data['frame_width'], self.sprite_data['frame_height'])
                if (fallback_rect.right <= self.sprite_sheet.get_width() and
                    fallback_rect.bottom <= self.sprite_sheet.get_height()):
                    frame_surface.blit(self.sprite_sheet, (0, 0), fallback_rect)
            return pygame.transform.scale(frame_surface, (self.width, self.height))
        except (IndexError, TypeError, AttributeError, KeyError):
            return pygame.Surface((self.width, self.height), pygame.SRCALPHA)

    def update_animation(self, dt):
        if not self.sprite_data:
            return
        try:
            if (self.current_animation not in self.sprite_data['animations'] or
                not self.sprite_data['animations'][self.current_animation]):
                self.current_animation = 'idle'
                self.frame_index = 0
            frames = self.sprite_data['animations'][self.current_animation]
            if not frames:
                return
            if self.frame_index >= len(frames):
                self.frame_index = 0
            self.animation_timer += dt
            if self.animation_timer >= self.frame_delay:
                self.frame_index = (self.frame_index + 1) % len(frames)
                self.animation_timer = 0
            self.frame_index = max(0, min(self.frame_index, len(frames) - 1))
            self.image = self.get_frame()
        except (AttributeError, KeyError, IndexError, TypeError):
            pass

class Building(Entity):
    def __init__(self, x, y, sprite_key):
        data = BUILDING_DATA.get(sprite_key, {'width': 100, 'height': 100, 'scale': 1.0, 'parallax_factor': 0.6})
        super().__init__(x, y, data['width'], data['height'], assets.get(sprite_key), None)
        self.parallax_factor = data['parallax_factor']
        self.base_y = y  # Store base y for depth sorting
        self.height_offset = -data['height'] * data['scale'] * 0.5  # Shift upward to simulate height

    def draw(self, screen, camera_x, camera_y):
        offset_x = self.x - camera_x * self.parallax_factor
        offset_y = self.y - camera_y * self.parallax_factor + self.height_offset
        screen.blit(self.image, (offset_x, offset_y))

class Player(Entity):
    def __init__(self, x, y, sprite_sheet_img):
        super().__init__(x, y, PLAYER_SPRITE_DATA['frame_width'], PLAYER_SPRITE_DATA['frame_height'],
                         sprite_sheet_img, PLAYER_SPRITE_DATA)
        self.speed = 250
        self.health = 100
        self.max_health = 100
        self.score = 0
        self.is_attacking = False
        self.attack_duration = 300
        self.attack_timer = 0
        self.attack_damage = 25
        self.facing_right = True
        self.invulnerable = False
        self.invulnerable_timer = 0
        self.invulnerable_duration = 1000
        self.is_grappling = False
        self.grapple_target_x = 0
        self.grapple_target_y = 0
        self.grapple_speed = 400
        self.grapple_range = 300
        self.gas_amount = 100
        self.max_gas = 100
        self.gas_consumption_rate = 20
        self.experience = 0
        self.level = 1
        self.experience_to_next_level = 100
        self.combo_count = 0
        self.last_attack_time = 0
        self.combo_window = 1000

    def update(self, dt, keys, mouse_pos, mouse_pressed):
        if not game_running:
            return
        self.update_animation(dt)
        if not self.is_grappling and self.gas_amount < self.max_gas:
            self.gas_amount = min(self.max_gas, self.gas_amount + 30 * dt / 1000)
        if mouse_pressed[0] and self.gas_amount > 0 and not self.is_grappling:
            world_mouse_x = mouse_pos[0] + camera_x
            world_mouse_y = mouse_pos[1] + camera_y
            dx = world_mouse_x - (self.x + self.width/2)
            dy = world_mouse_y - (self.y + self.height/2)
            distance = math.sqrt(dx*dx + dy*dy)
            if distance <= self.grapple_range:
                self.is_grappling = True
                self.grapple_target_x = world_mouse_x
                self.grapple_target_y = world_mouse_y
                play_sound('grapple_sound')
                create_grapple_particles(world_mouse_x, world_mouse_y)
        if mouse_pressed[2] and self.is_grappling:
            self.is_grappling = False
        if self.is_grappling and self.gas_amount > 0:
            self.gas_amount = max(0, self.gas_amount - self.gas_consumption_rate * dt / 1000)
            dx = self.grapple_target_x - (self.x + self.width/2)
            dy = self.grapple_target_y - (self.y + self.height/2)
            distance = math.sqrt(dx*dx + dy*dy)
            if distance > 10:
                move_x = (dx / distance) * self.grapple_speed * dt / 1000
                move_y = (dy / distance) * self.grapple_speed * dt / 1000
                self.x += move_x
                self.y += move_y
                self.current_animation = 'attack'
            else:
                self.is_grappling = False
        elif self.gas_amount <= 0:
            self.is_grappling = False
        if not self.is_grappling:
            move_x, move_y = 0, 0
            moving = False
            if keys[pygame.K_w] or keys[pygame.K_UP]:
                move_y -= 1
                moving = True
            if keys[pygame.K_s] or keys[pygame.K_DOWN]:
                move_y += 1
                moving = True
            if keys[pygame.K_a] or keys[pygame.K_LEFT]:
                move_x -= 1
                moving = True
                self.facing_right = False
            if keys[pygame.K_d] or keys[pygame.K_RIGHT]:
                move_x += 1
                moving = True
                self.facing_right = True
            if move_x != 0 and move_y != 0:
                factor = (self.speed * dt / 1000) / (2**0.5)
                self.x += move_x * factor
                self.y += move_y * factor
            else:
                self.x += move_x * self.speed * dt / 1000
                self.y += move_y * self.speed * dt / 1000
        self.x = max(0, min(WORLD_WIDTH - self.width, self.x))
        self.y = max(0, min(WORLD_HEIGHT - self.height, self.y))
        self.rect.topleft = (self.x, self.y)
        current_time = pygame.time.get_ticks()
        if self.is_attacking:
            self.attack_timer += dt
            if self.attack_timer >= self.attack_duration:
                self.is_attacking = False
                self.attack_timer = 0
                if not self.is_grappling:
                    self.current_animation = 'idle'
                self.frame_index = 0
        elif keys[pygame.K_SPACE] and not self.is_attacking:
            self.is_attacking = True
            self.attack_timer = 0
            self.current_animation = 'attack'
            self.frame_index = 0
            if current_time - self.last_attack_time < self.combo_window:
                self.combo_count += 1
            else:
                self.combo_count = 1
            self.last_attack_time = current_time
            play_sound('hit_sound')
        elif not self.is_grappling:
            if keys[pygame.K_w] or keys[pygame.K_s] or keys[pygame.K_a] or keys[pygame.K_d]:
                self.current_animation = 'walk_right' if self.facing_right else 'walk_left'
            else:
                self.current_animation = 'idle'
                self.frame_index = 0
        if self.invulnerable:
            self.invulnerable_timer += dt
            if self.invulnerable_timer >= self.invulnerable_duration:
                self.invulnerable = False
                self.invulnerable_timer = 0

    def take_damage(self, damage):
        if not self.invulnerable:
            self.health -= damage
            play_player_hit_sound()
            self.invulnerable = True
            self.invulnerable_timer = 0
            if self.health <= 0:
                self.health = 0
                return True
        return False

class Titan(Entity):
    def __init__(self, x, y, sprite_sheet_img):
        super().__init__(x, y, TITAN_SPRITE_DATA['frame_width'], TITAN_SPRITE_DATA['frame_height'],
                         sprite_sheet_img, TITAN_SPRITE_DATA)
        self.speed = random.randint(50, 100)
        self.health = 100
        self.attack_damage = 10
        self.attack_cooldown = 1000
        self.attack_timer = 0
        self.hit_by_player = False

    def update(self, dt, player_pos):
        if not game_running:
            return
        self.update_animation(dt)
        dx = player_pos[0] - self.x
        dy = player_pos[1] - self.y
        dist = (dx**2 + dy**2)**0.5
        if dist > 0:
            self.x += (dx / dist) * self.speed * dt / 1000
            self.y += (dy / dist) * self.speed * dt / 1000
        self.rect.topleft = (self.x, self.y)
        if dist < 100:
            self.current_animation = 'attack'
        else:
            self.current_animation = 'walk'
        self.attack_timer += dt

# --- Game Functions ---
def check_collision(rect1, rect2):
    return rect1.colliderect(rect2)

def spawn_titan(player_pos_world):
    while True:
        side = random.choice(['top', 'bottom', 'left', 'right'])
        if side == 'top':
            x = random.uniform(0, WORLD_WIDTH - TITAN_SPRITE_DATA['frame_width'] * TITAN_SPRITE_DATA['scale'])
            y = 0 - 200
        elif side == 'bottom':
            x = random.uniform(0, WORLD_WIDTH - TITAN_SPRITE_DATA['frame_width'] * TITAN_SPRITE_DATA['scale'])
            y = WORLD_HEIGHT + 200
        elif side == 'left':
            x = 0 - 200
            y = random.uniform(0, WORLD_HEIGHT - TITAN_SPRITE_DATA['frame_height'] * TITAN_SPRITE_DATA['scale'])
        else:
            x = WORLD_WIDTH + 200
            y = random.uniform(0, WORLD_HEIGHT - TITAN_SPRITE_DATA['frame_height'] * TITAN_SPRITE_DATA['scale'])
        if (abs(x - player_pos_world[0]) > SCREEN_WIDTH / 2 or
            abs(y - player_pos_world[1]) > SCREEN_HEIGHT / 2):
            break
    return Titan(x, y, assets['beast_titan_sprite'])

def spawn_buildings():
    buildings = []
    building_positions = [
        (1000, 1000, 'side_building1'),
        (1500, 1200, 'side_building2'),
        (2000, 800, 'side_building1'),
        (2500, 1500, 'side_building2'),
        (3000, 900, 'side_building1'),
        (3500, 1300, 'side_building2'),
    ]
    for x, y, sprite_key in building_positions:
        buildings.append(Building(x, y, sprite_key))
    return buildings

def update_game_info(player_obj, wave_num, titans_remaining):
    hud_height = 80
    hud_surface = pygame.Surface((SCREEN_WIDTH, hud_height), pygame.SRCALPHA)
    hud_surface.fill((0, 0, 0, 200))
    screen.blit(hud_surface, (0, 0))
    font_medium = pygame.font.Font(None, 32)
    font_small = pygame.font.Font(None, 24)
    health_percentage = player_obj.health / player_obj.max_health
    health_bar_width = 200
    health_bar_height = 20
    health_bar_x = 20
    health_bar_y = 15
    pygame.draw.rect(screen, DARK_RED, (health_bar_x, health_bar_y, health_bar_width, health_bar_height))
    pygame.draw.rect(screen, RED, (health_bar_x, health_bar_y, health_bar_width * health_percentage, health_bar_height))
    pygame.draw.rect(screen, WHITE, (health_bar_x, health_bar_y, health_bar_width, health_bar_height), 2)
    health_text = font_small.render(f"HP: {player_obj.health}/{player_obj.max_health}", True, WHITE)
    screen.blit(health_text, (health_bar_x, health_bar_y + 25))
    gas_percentage = player_obj.gas_amount / player_obj.max_gas
    gas_bar_x = 20
    gas_bar_y = 50
    pygame.draw.rect(screen, (50, 50, 50), (gas_bar_x, gas_bar_y, health_bar_width, health_bar_height))
    pygame.draw.rect(screen, BLUE, (gas_bar_x, gas_bar_y, health_bar_width * gas_percentage, health_bar_height))
    pygame.draw.rect(screen, WHITE, (gas_bar_x, gas_bar_y, health_bar_width, health_bar_height), 2)
    gas_text = font_small.render(f"Gas: {int(player_obj.gas_amount)}/{player_obj.max_gas}", True, WHITE)
    screen.blit(gas_text, (gas_bar_x + health_bar_width + 10, gas_bar_y + 2))
    score_text = font_medium.render(f"Score: {player_obj.score}", True, WHITE)
    wave_text = font_medium.render(f"Wave: {wave_num}", True, YELLOW)
    level_text = font_medium.render(f"Level: {player_obj.level}", True, GREEN)
    score_rect = score_text.get_rect(topright=(SCREEN_WIDTH - 20, 10))
    wave_rect = wave_text.get_rect(topright=(SCREEN_WIDTH - 20, 35))
    level_rect = level_text.get_rect(topright=(SCREEN_WIDTH - 200, 10))
    screen.blit(score_text, score_rect)
    screen.blit(wave_text, wave_rect)
    screen.blit(level_text, level_rect)
    if player_obj.combo_count > 1:
        combo_text = font_medium.render(f"COMBO x{player_obj.combo_count}!", True, GOLD)
        combo_rect = combo_text.get_rect(center=(SCREEN_WIDTH/2, 100))
        screen.blit(combo_text, combo_rect)
    if player_obj.level < 10:
        exp_bar_width = SCREEN_WIDTH - 40
        exp_bar_height = 10
        exp_bar_x = 20
        exp_bar_y = SCREEN_HEIGHT - 30
        exp_percentage = player_obj.experience / player_obj.experience_to_next_level
        pygame.draw.rect(screen, (50, 50, 50), (exp_bar_x, exp_bar_y, exp_bar_width, exp_bar_height))
        pygame.draw.rect(screen, GOLD, (exp_bar_x, exp_bar_y, exp_bar_width * exp_percentage, exp_bar_height))
        pygame.draw.rect(screen, WHITE, (exp_bar_x, exp_bar_y, exp_bar_width, exp_bar_height), 1)
        exp_text = font_small.render(f"EXP: {player_obj.experience}/{player_obj.experience_to_next_level}", True, WHITE)
        screen.blit(exp_text, (exp_bar_x, exp_bar_y - 20))

def play_sound(sound_key):
    if assets.get(sound_key) and assets[sound_key]:
        try:
            assets[sound_key].play()
        except pygame.error:
            pass

def play_attack_sound():
    play_sound('hit_sound')

def play_hit_sound():
    play_sound('beast_hit_sound')

def play_player_hit_sound():
    play_sound('hit_sound')

def play_game_over_sound():
    stop_music()

def draw_menu():
    screen.fill(BLACK)
    loading_image = assets.get('loading_screen')
    if loading_image:
        loading_image = pygame.transform.scale(loading_image, (SCREEN_WIDTH, SCREEN_HEIGHT))
        screen.blit(loading_image, (0, 0))
    overlay = pygame.Surface((SCREEN_WIDTH, SCREEN_HEIGHT))
    overlay.set_alpha(128)
    overlay.fill(BLACK)
    screen.blit(overlay, (0, 0))
    font_large = pygame.font.Font(None, 100)
    font_medium = pygame.font.Font(None, 50)
    font_small = pygame.font.Font(None, 30)
    title_text = font_large.render("ATTACK ON TITAN", True, RED)
    subtitle_text = font_large.render("SURVIVE", True, GOLD)
    title_rect = title_text.get_rect(center=(SCREEN_WIDTH/2, 150))
    subtitle_rect = subtitle_text.get_rect(center=(SCREEN_WIDTH/2, 220))
    screen.blit(title_text, title_rect)
    screen.blit(subtitle_text, subtitle_rect)
    char_text = font_medium.render("Choose Your Character:", True, WHITE)
    char_rect = char_text.get_rect(center=(SCREEN_WIDTH/2, 320))
    screen.blit(char_text, char_rect)
    levi_color = GOLD if selected_character == 'levi' else WHITE
    mikasa_color = GOLD if selected_character == 'mikasa' else WHITE
    levi_text = font_medium.render("LEVI", True, levi_color)
    mikasa_text = font_medium.render("MIKASA", True, mikasa_color)
    levi_rect = levi_text.get_rect(center=(SCREEN_WIDTH/2 - 150, 400))
    mikasa_rect = mikasa_text.get_rect(center=(SCREEN_WIDTH/2 + 150, 400))
    if selected_character == 'levi':
        pygame.draw.rect(screen, DARK_RED, levi_rect.inflate(20, 10), border_radius=5)
    if selected_character == 'mikasa':
        pygame.draw.rect(screen, DARK_RED, mikasa_rect.inflate(20, 10), border_radius=5)
    screen.blit(levi_text, levi_rect)
    screen.blit(mikasa_text, mikasa_rect)
    start_text = font_medium.render("PRESS SPACE TO START", True, GREEN)
    start_rect = start_text.get_rect(center=(SCREEN_WIDTH/2, 500))
    screen.blit(start_text, start_rect)
    instructions = [
        "WASD - Move",
        "Left Click - Grapple (ODM Gear)",
        "Right Click - Release Grapple",
        "Space - Attack",
        "ESC - Pause"
    ]
    for i, instruction in enumerate(instructions):
        inst_text = font_small.render(instruction, True, WHITE)
        inst_rect = inst_text.get_rect(center=(SCREEN_WIDTH/2, 580 + i * 25))
        screen.blit(inst_text, inst_rect)
    pygame.display.flip()
    return levi_rect, mikasa_rect

def run_loading_screen():
    loading_image = assets.get('loading_screen')
    if loading_image:
        loading_image = pygame.transform.scale(loading_image, (SCREEN_WIDTH, SCREEN_HEIGHT))
        screen.blit(loading_image, (0, 0))
    else:
        screen.fill(BLACK)
        font = pygame.font.Font(None, 74)
        text_surf = font.render("Loading...", True, WHITE)
        text_rect = text_surf.get_rect(center=(SCREEN_WIDTH / 2, SCREEN_HEIGHT / 2))
        screen.blit(text_surf, text_rect)
    pygame.display.flip()

def game_over_screen(player_score, wave_num, total_kills_count):
    play_game_over_sound()
    screen.fill(BLACK)
    loading_image = assets.get('loading_screen')
    if loading_image:
        loading_image = pygame.transform.scale(loading_image, (SCREEN_WIDTH, SCREEN_HEIGHT))
        screen.blit(loading_image, (0, 0))
    overlay = pygame.Surface((SCREEN_WIDTH, SCREEN_HEIGHT))
    overlay.set_alpha(180)
    overlay.fill(BLACK)
    screen.blit(overlay, (0, 0))
    font_large = pygame.font.Font(None, 100)
    font_medium = pygame.font.Font(None, 50)
    font_small = pygame.font.Font(None, 40)
    game_over_text = font_large.render("HUMANITY FELL", True, RED)
    score_text = font_medium.render(f"Final Score: {player_score}", True, WHITE)
    wave_text = font_medium.render(f"Wave Reached: {wave_num}", True, WHITE)
    kills_text = font_medium.render(f"Titans Slain: {total_kills_count}", True, WHITE)
    restart_text = font_small.render("R - Restart", True, GREEN)
    menu_text = font_small.render("M - Main Menu", True, YELLOW)
    quit_text = font_small.render("ESC - Quit", True, RED)
    game_over_rect = game_over_text.get_rect(center=(SCREEN_WIDTH / 2, 200))
    score_rect = score_text.get_rect(center=(SCREEN_WIDTH / 2, 320))
    wave_rect = wave_text.get_rect(center=(SCREEN_WIDTH / 2, 370))
    kills_rect = kills_text.get_rect(center=(SCREEN_WIDTH / 2, 420))
    restart_rect = restart_text.get_rect(center=(SCREEN_WIDTH / 2, 520))
    menu_rect = menu_text.get_rect(center=(SCREEN_WIDTH / 2, 560))
    quit_rect = quit_text.get_rect(center=(SCREEN_WIDTH / 2, 600))
    screen.blit(game_over_text, game_over_rect)
    screen.blit(score_text, score_rect)
    screen.blit(wave_text, wave_rect)
    screen.blit(kills_text, kills_rect)
    screen.blit(restart_text, restart_rect)
    screen.blit(menu_text, menu_rect)
    screen.blit(quit_text, quit_rect)
    pygame.display.flip()

def pause_screen():
    overlay = pygame.Surface((SCREEN_WIDTH, SCREEN_HEIGHT))
    overlay.set_alpha(128)
    overlay.fill(BLACK)
    screen.blit(overlay, (0, 0))
    font_large = pygame.font.Font(None, 100)
    font_medium = pygame.font.Font(None, 50)
    pause_text = font_large.render("PAUSED", True, WHITE)
    resume_text = font_medium.render("ESC - Resume", True, GREEN)
    menu_text = font_medium.render("M - Main Menu", True, YELLOW)
    pause_rect = pause_text.get_rect(center=(SCREEN_WIDTH / 2, SCREEN_HEIGHT / 2 - 50))
    resume_rect = resume_text.get_rect(center=(SCREEN_WIDTH / 2, SCREEN_HEIGHT / 2 + 50))
    menu_rect = menu_text.get_rect(center=(SCREEN_WIDTH / 2, SCREEN_HEIGHT / 2 + 100))
    screen.blit(pause_text, pause_rect)
    screen.blit(resume_text, resume_rect)
    screen.blit(menu_text, menu_rect)
    pygame.display.flip()

def reset_game():
    global player, titans, buildings, titan_spawn_timer, camera_x, camera_y
    global wave_number, titans_killed_this_wave, titans_needed_for_next_wave, total_kills
    sprite_key = 'player_sprite' if selected_character == 'levi' else 'mikasa_sprite'
    player = Player(WORLD_WIDTH / 2, WORLD_HEIGHT / 2, assets[sprite_key])
    titans = []
    buildings = spawn_buildings()
    titan_spawn_timer = 0
    camera_x, camera_y = 0, 0
    wave_number = 1
    titans_killed_this_wave = 0
    titans_needed_for_next_wave = 5
    total_kills = 0
    for _ in range(3):
        titans.append(spawn_titan((player.x, player.y)))
    music_tracks = ['game_music_1', 'game_music_2', 'game_music_3']
    selected_track = random.choice(music_tracks)
    if assets.get(selected_track):
        play_music(assets[selected_track])

async def main():
    global game_state, camera_x, camera_y, titan_spawn_timer, selected_character
    global wave_number, titans_killed_this_wave, titans_needed_for_next_wave, total_kills
    global assets, player, titans, buildings

    clock = pygame.time.Clock()
    assets = load_assets()
    if assets.get('menu_music'):
        play_music(assets['menu_music'])
    running = True
    levi_rect = mikasa_rect = None
    buildings = []

    while running:
        if game_state == 'MENU':
            dt = clock.tick(MENU_FPS) / 1000.0
        else:
            dt = clock.tick(FPS) / 1000.0

        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                global game_running
                game_running = False
                running = False
            elif event.type == pygame.KEYDOWN:
                if game_state == 'MENU':
                    if event.key == pygame.K_SPACE:
                        game_state = 'PLAYING'
                        reset_game()
                    elif event.key == pygame.K_LEFT or event.key == pygame.K_a:
                        selected_character = 'levi'
                    elif event.key == pygame.K_RIGHT or event.key == pygame.K_d:
                        selected_character = 'mikasa'
                elif game_state == 'PLAYING':
                    if event.key == pygame.K_ESCAPE:
                        game_state = 'PAUSED'
                elif game_state == 'PAUSED':
                    if event.key == pygame.K_ESCAPE:
                        game_state = 'PLAYING'
                    elif event.key == pygame.K_m:
                        game_state = 'MENU'
                        if assets.get('menu_music'):
                            play_music(assets['menu_music'])
                elif game_state == 'GAME_OVER':
                    if event.key == pygame.K_r:
                        game_state = 'PLAYING'
                        reset_game()
                    elif event.key == pygame.K_m:
                        game_state = 'MENU'
                        if assets.get('menu_music'):
                            play_music(assets['menu_music'])
                    elif event.key == pygame.K_ESCAPE:
                        running = False
            elif event.type == pygame.MOUSEBUTTONDOWN:
                if game_state == 'MENU' and levi_rect and mikasa_rect:
                    if levi_rect.collidepoint(event.pos):
                        selected_character = 'levi'
                    elif mikasa_rect.collidepoint(event.pos):
                        selected_character = 'mikasa'

        if game_state == 'MENU':
            levi_rect, mikasa_rect = draw_menu()
        elif game_state == 'LOADING':
            run_loading_screen()
        elif game_state == 'PLAYING':
            keys = pygame.key.get_pressed()
            mouse_pos = pygame.mouse.get_pos()
            mouse_pressed = pygame.mouse.get_pressed()
            player.update(dt * 1000, keys, mouse_pos, mouse_pressed)
            camera_x = player.x - SCREEN_WIDTH / 2
            camera_y = player.y - SCREEN_HEIGHT / 2
            camera_x = max(0, min(WORLD_WIDTH - SCREEN_WIDTH, camera_x))
            camera_y = max(0, min(WORLD_HEIGHT - SCREEN_HEIGHT, camera_y))
            max_titans = min(15, 5 + wave_number * 2)
            titan_spawn_timer += dt * 1000
            spawn_interval = max(1000, 3000 - wave_number * 200)
            if titan_spawn_timer >= spawn_interval and len(titans) < max_titans:
                titans.append(spawn_titan((player.x, player.y)))
                titan_spawn_timer = 0
            for titan in titans:
                titan.update(dt * 1000, (player.x, player.y))
            update_particles(dt * 1000)
            if player.is_attacking:
                attack_range = 80 + (player.combo_count * 10)
                player_attack_rect = player.rect.inflate(attack_range, attack_range)
                for titan in titans:
                    if check_collision(player_attack_rect, titan.rect):
                        if not titan.hit_by_player:
                            damage = player.attack_damage + (player.combo_count * 5)
                            titan.health -= damage
                            titan.hit_by_player = True
                            play_hit_sound()
                            create_hit_particles(titan.x + titan.width/2, titan.y + titan.height/2)
                            if titan.health <= 0:
                                base_score = 100
                                combo_bonus = player.combo_count * 25
                                wave_bonus = wave_number * 10
                                total_score = base_score + combo_bonus + wave_bonus
                                player.score += total_score
                                player.experience += 20 + wave_number * 5
                                titans_killed_this_wave += 1
                                total_kills += 1
                                if player.experience >= player.experience_to_next_level and player.level < 10:
                                    player.level += 1
                                    player.experience = 0
                                    player.experience_to_next_level += 50
                                    player.max_health += 20
                                    player.health = player.max_health
                                    player.attack_damage += 5
                                    play_sound('jump_sound')
                    else:
                        titan.hit_by_player = False
            for titan in titans:
                if check_collision(player.rect, titan.rect):
                    if titan.attack_timer >= titan.attack_cooldown:
                        damage = titan.attack_damage + (wave_number - 1) * 2
                        if player.take_damage(damage):
                            game_state = 'GAME_OVER'
                        titan.attack_timer = 0
            titans[:] = [titan for titan in titans if titan.health > 0]
            if titans_killed_this_wave >= titans_needed_for_next_wave:
                wave_number += 1
                titans_killed_this_wave = 0
                titans_needed_for_next_wave += 3
                player.score += wave_number * 50
                player.experience += wave_number * 10
                player.health = min(player.max_health, player.health + 20)
            screen.fill(BLACK)
            if assets.get('shiganshina'):
                parallax_offset_shiganshina_x = -camera_x * 0.1
                parallax_offset_shiganshina_y = -camera_y * 0.1
                shiganshina_img = assets['shiganshina']
                shiganshina_width = shiganshina_img.get_width()
                shiganshina_height = shiganshina_img.get_height()
                start_x = int((camera_x * 0.1) / shiganshina_width) * shiganshina_width
                start_y = int((camera_y * 0.1) / shiganshina_height) * shiganshina_height
                for x in range(int(start_x), int(start_x + SCREEN_WIDTH + shiganshina_width), shiganshina_width):
                    for y in range(int(start_y), int(start_y + SCREEN_HEIGHT + shiganshina_height), shiganshina_height):
                        screen.blit(shiganshina_img, (x - camera_x * 0.1, y - camera_y * 0.1))
            if assets.get('bg_buildings'):
                parallax_offset_buildings_x = -camera_x * 0.3
                parallax_offset_buildings_y = -camera_y * 0.3
                bg_buildings_img = assets['bg_buildings']
                scaled_height = int(bg_buildings_img.get_height() * WORLD_WIDTH / bg_buildings_img.get_width())
                bg_buildings_scaled = pygame.transform.scale(bg_buildings_img, (WORLD_WIDTH, scaled_height))
                bg_y_offset = (WORLD_HEIGHT - scaled_height) / 2
                screen.blit(bg_buildings_scaled, (parallax_offset_buildings_x, bg_y_offset + parallax_offset_buildings_y))
            # Sort entities and buildings by y-position for depth
            render_list = [(b, b.base_y) for b in buildings] + [(t, t.y) for t in titans] + [(player, player.y)]
            render_list.sort(key=lambda x: x[1])  # Sort by y-position
            for entity, _ in render_list:
                if isinstance(entity, Building):
                    entity.draw(screen, camera_x, camera_y)
                else:
                    entity_image = entity.image
                    if isinstance(entity, Player) and not entity.facing_right:
                        entity_image = pygame.transform.flip(entity_image, True, False)
                    if entity.invulnerable and entity.invulnerable_timer % 200 < 100:
                        entity_image.set_alpha(100)
                    else:
                        entity_image.set_alpha(255)
                    screen.blit(entity_image, (entity.x - camera_x, entity.y - camera_y))
            if player.is_grappling:
                start_pos = (player.x + player.width/2 - camera_x, player.y + player.height/2 - camera_y)
                end_pos = (player.grapple_target_x - camera_x, player.grapple_target_y - camera_y)
                pygame.draw.line(screen, BLUE, start_pos, end_pos, 3)
                pygame.draw.circle(screen, YELLOW, (int(end_pos[0]), int(end_pos[1])), 8, 2)
            draw_particles(screen, camera_x, camera_y)
            update_game_info(player, wave_number, len(titans))
            pygame.display.flip()
        elif game_state == 'PAUSED':
            pause_screen()
        elif game_state == 'GAME_OVER':
            game_over_screen(player.score, wave_number, total_kills)
        await asyncio.sleep(1.0 / FPS)

if platform.system() == "Emscripten":
    asyncio.ensure_future(main())
else:
    if __name__ == "__main__":
        asyncio.run(main())