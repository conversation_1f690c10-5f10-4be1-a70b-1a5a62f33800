import pygame
import random
import os
import sys
import math

# --- Pygame Initialization ---
pygame.init()
pygame.mixer.init(frequency=22050, size=-16, channels=2, buffer=512)

# --- Game Constants ---
SCREEN_WIDTH = 1000
SCREEN_HEIGHT = 700
FPS = 60
MENU_FPS = 30

# Set up the display
screen = pygame.display.set_mode((SCREEN_WIDTH, SCREEN_HEIGHT))
pygame.display.set_caption("Attack on Titan: Survive")

# Colors
WHITE = (255, 255, 255)
BLACK = (0, 0, 0)
RED = (255, 0, 0)
GREEN = (0, 255, 0)
BLUE = (0, 0, 255)
YELLOW = (255, 255, 0)
GRAY = (150, 150, 150)
DARK_RED = (139, 0, 0)
GOLD = (255, 215, 0)

# Game World Dimensions
WORLD_WIDTH = 6000
WORLD_HEIGHT = 6000

# --- Asset Paths ---
ASSET_DIR = ""

IMAGE_PATHS = {
    'loading_screen': os.path.join(ASSET_DIR, 'AOT_loadingscreen.png'),
    'player_sprite': os.path.join(ASSET_DIR, 'levi.png'),
    'mikasa_sprite': os.path.join(ASSET_DIR, 'mikasa.png'),
    'beast_titan_sprite': os.path.join(ASSET_DIR, 'attack-on-titan-beast-titan-1.png'),
    'titan_sprite': os.path.join(ASSET_DIR, 'titan.png'),
    'bg_buildings': os.path.join(ASSET_DIR, 'AOT Background buildings.png'),
    'midground': os.path.join(ASSET_DIR, 'midground.png'),
    'foreground': os.path.join(ASSET_DIR, 'foreground.png'),
    'shiganshina': os.path.join(ASSET_DIR, 'shiganshina.png')
}

SOUND_PATHS = {
    'menu_music': os.path.join(ASSET_DIR, 'menu_ost.mp3'),
    'game_music_1': os.path.join(ASSET_DIR, 'game_ost(1).mp3'),
    'game_music_2': os.path.join(ASSET_DIR, 'game_ost(2).mp3'),
    'game_music_3': os.path.join(ASSET_DIR, 'game_ost(3).mp3'),
    'grapple_sound': os.path.join(ASSET_DIR, 'grapple.mp3'),
    'hit_sound': os.path.join(ASSET_DIR, 'hit.mp3'),
    'beast_hit_sound': os.path.join(ASSET_DIR, 'beast_hit.mp3'),
    'jump_sound': os.path.join(ASSET_DIR, 'jump.mp3')
}

# --- Asset Loading ---
def load_assets():
    """Loads all game images and sounds."""
    assets = {}

    # Load images
    for key, path in IMAGE_PATHS.items():
        try:
            image = pygame.image.load(path).convert_alpha()
            assets[key] = image
            print(f"Loaded image: {path}")
        except pygame.error as e:
            print(f"Error loading image {path}: {e}")
            # Load a placeholder if image fails to load
            placeholder_surface = pygame.Surface((100, 100))
            placeholder_surface.fill(GRAY)
            font = pygame.font.Font(None, 20)
            text_surf = font.render(key, True, BLACK)
            text_rect = text_surf.get_rect(center=(50, 50))
            placeholder_surface.blit(text_surf, text_rect)
            assets[key] = placeholder_surface

    # Load sounds
    for key, path in SOUND_PATHS.items():
        try:
            if 'music' in key:
                # Don't load music into memory, just store the path
                assets[key] = path
                print(f"Registered music: {path}")
            else:
                sound = pygame.mixer.Sound(path)
                assets[key] = sound
                print(f"Loaded sound: {path}")
        except pygame.error as e:
            print(f"Error loading sound {path}: {e}")
            assets[key] = None

    return assets

# --- Music Management ---
current_music = None
music_volume = 0.7

def play_music(music_path, loops=-1):
    """Play background music."""
    global current_music
    if music_path and current_music != music_path:
        try:
            pygame.mixer.music.load(music_path)
            pygame.mixer.music.set_volume(music_volume)
            pygame.mixer.music.play(loops)
            current_music = music_path
        except pygame.error as e:
            print(f"Error playing music {music_path}: {e}")

def stop_music():
    """Stop background music."""
    global current_music
    pygame.mixer.music.stop()
    current_music = None

# --- Game State ---
game_state = 'MENU' # MENU, LOADING, PLAYING, PAUSED, GAME_OVER
assets = {} # Will store loaded images and sounds
camera_x, camera_y = 0, 0 # Camera position
selected_character = 'levi' # 'levi' or 'mikasa'

# Game statistics
total_kills = 0
total_score = 0
wave_number = 1
titans_killed_this_wave = 0
titans_needed_for_next_wave = 5

# --- Sprite Sheet Data (Based on actual sprite sheets) ---
# Levi sprite sheet: 3 frames wide, 2 frames tall (61x58 each frame)
PLAYER_SPRITE_DATA = {
    'frame_width': 61,
    'frame_height': 58,
    'scale': 2.0,
    'animations': {
        'idle': [(0, 0), (0, 1), (0, 2)],  # Top row frames 0-2
        'walk_right': [(1, 0)],  # Bottom row frame 0
        'walk_left': [(1, 0)],   # Same frame, will be flipped
        'attack': [(1, 1)]       # Bottom row frame 1
    }
}

# Beast Titan sprite sheet: Simplified for stability
TITAN_SPRITE_DATA = {
    'frame_width': 96,
    'frame_height': 96,
    'scale': 2.0,
    'animations': {
        'idle': [(0, 0)],
        'walk': [(0, 0)],
        'attack': [(0, 0)]
    }
}

# --- Particle System ---
class Particle:
    def __init__(self, x, y, color, velocity_x, velocity_y, lifetime):
        self.x = x
        self.y = y
        self.color = color
        self.velocity_x = velocity_x
        self.velocity_y = velocity_y
        self.lifetime = lifetime
        self.max_lifetime = lifetime

    def update(self, dt):
        self.x += self.velocity_x * dt / 1000
        self.y += self.velocity_y * dt / 1000
        self.lifetime -= dt
        return self.lifetime > 0

    def draw(self, screen, camera_x, camera_y):
        alpha = int(255 * (self.lifetime / self.max_lifetime))
        color_with_alpha = (*self.color[:3], alpha)

        # Create a surface for the particle with alpha
        particle_surface = pygame.Surface((4, 4), pygame.SRCALPHA)
        particle_surface.fill(color_with_alpha)
        screen.blit(particle_surface, (self.x - camera_x, self.y - camera_y))

particles = []

def create_hit_particles(x, y, color=RED):
    """Create particles when a titan is hit."""
    for _ in range(8):
        velocity_x = random.uniform(-100, 100)
        velocity_y = random.uniform(-100, 100)
        particles.append(Particle(x, y, color, velocity_x, velocity_y, 500))

def create_grapple_particles(x, y):
    """Create particles for grappling hook effect."""
    for _ in range(3):
        velocity_x = random.uniform(-50, 50)
        velocity_y = random.uniform(-50, 50)
        particles.append(Particle(x, y, BLUE, velocity_x, velocity_y, 300))

def update_particles(dt):
    """Update all particles and remove dead ones."""
    global particles
    particles = [p for p in particles if p.update(dt)]

def draw_particles(screen, camera_x, camera_y):
    """Draw all particles."""
    for particle in particles:
        particle.draw(screen, camera_x, camera_y)

# --- Game Entities ---
class Entity(pygame.sprite.Sprite):
    """Base class for player and enemies."""
    def __init__(self, x, y, width, height, sprite_sheet_img, sprite_data):
        super().__init__()
        self.x = x
        self.y = y
        self.original_width = width
        self.original_height = height
        self.sprite_sheet = sprite_sheet_img
        self.sprite_data = sprite_data
        self.current_animation = 'idle'
        self.frame_index = 0
        self.animation_timer = 0
        self.frame_delay = 100 # milliseconds per frame (10 FPS default)

        # Scale based on sprite_data
        self.width = int(self.original_width * self.sprite_data['scale'])
        self.height = int(self.original_height * self.sprite_data['scale'])

        # Create a surface for the current frame
        self.image = pygame.Surface((self.width, self.height), pygame.SRCALPHA) # SRCALPHA for transparency
        self.rect = self.image.get_rect(topleft=(self.x, self.y)) # Initial rect

    def get_frame(self):
        """Extracts and scales the current frame from the sprite sheet."""
        # Ensure we have a valid animation
        if (self.current_animation not in self.sprite_data['animations'] or
            not self.sprite_data['animations'][self.current_animation]):
            self.current_animation = 'idle'
            self.frame_index = 0

        # Fallback to idle if current animation doesn't exist
        if (self.current_animation not in self.sprite_data['animations'] or
            not self.sprite_data['animations'][self.current_animation]):
            return pygame.Surface((self.width, self.height), pygame.SRCALPHA)

        frames = self.sprite_data['animations'][self.current_animation]

        # Safety check for empty frames list
        if len(frames) == 0:
            return pygame.Surface((self.width, self.height), pygame.SRCALPHA)

        # Ensure frame_index is always within valid bounds
        self.frame_index = max(0, min(self.frame_index, len(frames) - 1))

        try:
            row, col = frames[self.frame_index]
        except (IndexError, TypeError):
            # If there's any error, use the first frame
            self.frame_index = 0
            if len(frames) > 0:
                row, col = frames[0]
            else:
                return pygame.Surface((self.width, self.height), pygame.SRCALPHA)

        frame_rect = pygame.Rect(
            col * self.sprite_data['frame_width'],
            row * self.sprite_data['frame_height'],
            self.sprite_data['frame_width'],
            self.sprite_data['frame_height']
        )

        # Create a new surface to draw the frame onto
        frame_surface = pygame.Surface((self.sprite_data['frame_width'], self.sprite_data['frame_height']), pygame.SRCALPHA)

        # Check if the sprite sheet is large enough for this frame
        if (self.sprite_sheet and
            frame_rect.right <= self.sprite_sheet.get_width() and
            frame_rect.bottom <= self.sprite_sheet.get_height()):
            frame_surface.blit(self.sprite_sheet, (0, 0), frame_rect)
        else:
            # If frame is out of bounds, use the first frame (0,0)
            fallback_rect = pygame.Rect(0, 0, self.sprite_data['frame_width'], self.sprite_data['frame_height'])
            if (self.sprite_sheet and
                fallback_rect.right <= self.sprite_sheet.get_width() and
                fallback_rect.bottom <= self.sprite_sheet.get_height()):
                frame_surface.blit(self.sprite_sheet, (0, 0), fallback_rect)

        return pygame.transform.scale(frame_surface, (self.width, self.height))

    def update_animation(self, dt):
        """Updates the animation frame based on delta time."""
        # Ensure we have a valid animation
        if (self.current_animation not in self.sprite_data['animations'] or
            not self.sprite_data['animations'][self.current_animation]):
            self.current_animation = 'idle'
            self.frame_index = 0

        # Double-check idle exists
        if (self.current_animation not in self.sprite_data['animations'] or
            not self.sprite_data['animations'][self.current_animation]):
            return

        frames = self.sprite_data['animations'][self.current_animation]
        if len(frames) == 0:
            return

        # Ensure frame_index is within bounds before updating
        if self.frame_index >= len(frames):
            self.frame_index = 0

        self.animation_timer += dt
        if self.animation_timer >= self.frame_delay:
            self.frame_index = (self.frame_index + 1) % len(frames)
            self.animation_timer = 0

        # Final safety check
        self.frame_index = max(0, min(self.frame_index, len(frames) - 1))

        self.image = self.get_frame()

class Player(Entity):
    def __init__(self, x, y, sprite_sheet_img):
        super().__init__(x, y, PLAYER_SPRITE_DATA['frame_width'], PLAYER_SPRITE_DATA['frame_height'],
                         sprite_sheet_img, PLAYER_SPRITE_DATA)
        self.speed = 250
        self.health = 100
        self.max_health = 100
        self.score = 0
        self.is_attacking = False
        self.attack_duration = 300
        self.attack_timer = 0
        self.attack_damage = 25
        self.facing_right = True
        self.invulnerable = False
        self.invulnerable_timer = 0
        self.invulnerable_duration = 1000

        # ODM Gear mechanics
        self.is_grappling = False
        self.grapple_target_x = 0
        self.grapple_target_y = 0
        self.grapple_speed = 400
        self.grapple_range = 300
        self.gas_amount = 100
        self.max_gas = 100
        self.gas_consumption_rate = 20  # per second when grappling

        # Experience and leveling
        self.experience = 0
        self.level = 1
        self.experience_to_next_level = 100

        # Enhanced combat
        self.combo_count = 0
        self.last_attack_time = 0
        self.combo_window = 1000  # ms

    def update(self, dt, keys, mouse_pos, mouse_pressed):
        """Updates player position, animation, and state."""
        self.update_animation(dt)

        # Gas regeneration when not grappling
        if not self.is_grappling and self.gas_amount < self.max_gas:
            self.gas_amount = min(self.max_gas, self.gas_amount + 30 * dt / 1000)

        # ODM Gear grappling
        if mouse_pressed[0] and self.gas_amount > 0 and not self.is_grappling:  # Left mouse button
            # Calculate grapple target
            world_mouse_x = mouse_pos[0] + camera_x
            world_mouse_y = mouse_pos[1] + camera_y

            # Check if target is within range
            dx = world_mouse_x - (self.x + self.width/2)
            dy = world_mouse_y - (self.y + self.height/2)
            distance = math.sqrt(dx*dx + dy*dy)

            if distance <= self.grapple_range:
                self.is_grappling = True
                self.grapple_target_x = world_mouse_x
                self.grapple_target_y = world_mouse_y
                play_sound('grapple_sound')
                create_grapple_particles(world_mouse_x, world_mouse_y)

        # Right click to release grapple
        if mouse_pressed[2] and self.is_grappling:  # Right mouse button
            self.is_grappling = False

        # Handle grappling movement
        if self.is_grappling and self.gas_amount > 0:
            # Consume gas
            self.gas_amount = max(0, self.gas_amount - self.gas_consumption_rate * dt / 1000)

            # Move towards grapple target
            dx = self.grapple_target_x - (self.x + self.width/2)
            dy = self.grapple_target_y - (self.y + self.height/2)
            distance = math.sqrt(dx*dx + dy*dy)

            if distance > 10:  # Still moving towards target
                move_x = (dx / distance) * self.grapple_speed * dt / 1000
                move_y = (dy / distance) * self.grapple_speed * dt / 1000
                self.x += move_x
                self.y += move_y
                self.current_animation = 'attack'  # Use attack animation for grappling
            else:
                self.is_grappling = False
        elif self.gas_amount <= 0:
            self.is_grappling = False

        # Regular movement (only if not grappling)
        if not self.is_grappling:
            move_x, move_y = 0, 0
            moving = False
            if keys[pygame.K_w] or keys[pygame.K_UP]:
                move_y -= 1
                moving = True
            if keys[pygame.K_s] or keys[pygame.K_DOWN]:
                move_y += 1
                moving = True
            if keys[pygame.K_a] or keys[pygame.K_LEFT]:
                move_x -= 1
                moving = True
                self.facing_right = False
            if keys[pygame.K_d] or keys[pygame.K_RIGHT]:
                move_x += 1
                moving = True
                self.facing_right = True

            # Normalize diagonal movement
            if move_x != 0 and move_y != 0:
                factor = (self.speed * dt / 1000) / (2**0.5)
                self.x += move_x * factor
                self.y += move_y * factor
            else:
                self.x += move_x * self.speed * dt / 1000
                self.y += move_y * self.speed * dt / 1000

        # Clamp player position within world bounds
        self.x = max(0, min(WORLD_WIDTH - self.width, self.x))
        self.y = max(0, min(WORLD_HEIGHT - self.height, self.y))
        self.rect.topleft = (self.x, self.y)

        # Attack logic
        current_time = pygame.time.get_ticks()
        if self.is_attacking:
            self.attack_timer += dt
            if self.attack_timer >= self.attack_duration:
                self.is_attacking = False
                self.attack_timer = 0
                if not self.is_grappling:
                    self.current_animation = 'idle'
                self.frame_index = 0
        elif keys[pygame.K_SPACE] and not self.is_attacking:
            self.is_attacking = True
            self.attack_timer = 0
            self.current_animation = 'attack'
            self.frame_index = 0

            # Combo system
            if current_time - self.last_attack_time < self.combo_window:
                self.combo_count += 1
            else:
                self.combo_count = 1
            self.last_attack_time = current_time

            play_sound('hit_sound')
        elif not self.is_grappling:
            if keys[pygame.K_w] or keys[pygame.K_s] or keys[pygame.K_a] or keys[pygame.K_d]:
                self.current_animation = 'walk_right' if self.facing_right else 'walk_left'
            else:
                self.current_animation = 'idle'
                self.frame_index = 0

        # Invulnerability
        if self.invulnerable:
            self.invulnerable_timer += dt
            if self.invulnerable_timer >= self.invulnerable_duration:
                self.invulnerable = False
                self.invulnerable_timer = 0

    def take_damage(self, damage):
        if not self.invulnerable:
            self.health -= damage
            play_player_hit_sound()
            self.invulnerable = True
            self.invulnerable_timer = 0
            if self.health <= 0:
                self.health = 0
                return True # Player is dead
        return False

class Titan(Entity):
    def __init__(self, x, y, sprite_sheet_img):
        super().__init__(x, y, TITAN_SPRITE_DATA['frame_width'], TITAN_SPRITE_DATA['frame_height'],
                         sprite_sheet_img, TITAN_SPRITE_DATA)
        self.speed = random.randint(50, 100) # Pixels per second
        self.health = 100
        self.attack_damage = 10
        self.attack_cooldown = 1000 # ms
        self.attack_timer = 0
        self.hit_by_player = False # To ensure only one hit per player attack

    def update(self, dt, player_pos):
        """Updates titan position, animation, and state."""
        self.update_animation(dt)

        # Move towards player
        dx = player_pos[0] - self.x
        dy = player_pos[1] - self.y
        dist = (dx**2 + dy**2)**0.5

        if dist > 0: # Avoid division by zero
            self.x += (dx / dist) * self.speed * dt / 1000
            self.y += (dy / dist) * self.speed * dt / 1000

        self.rect.topleft = (self.x, self.y)

        # Titan Animation (simple walk, or attack if very close)
        if dist < 100: # If close enough to attack
            self.current_animation = 'attack'
        else:
            self.current_animation = 'walk'

        # Attack cooldown
        self.attack_timer += dt

# --- Game Functions ---
def check_collision(rect1, rect2):
    """Checks for simple AABB collision between two pygame Rects."""
    return rect1.colliderect(rect2)

def spawn_titan(player_pos_world):
    """Spawns a titan at a random location far from the player."""
    while True:
        # Spawn outside visible screen area, but within world bounds
        side = random.choice(['top', 'bottom', 'left', 'right'])
        if side == 'top':
            x = random.uniform(0, WORLD_WIDTH - TITAN_SPRITE_DATA['frame_width'] * TITAN_SPRITE_DATA['scale'])
            y = 0 - 200 # Spawn above top edge
        elif side == 'bottom':
            x = random.uniform(0, WORLD_WIDTH - TITAN_SPRITE_DATA['frame_width'] * TITAN_SPRITE_DATA['scale'])
            y = WORLD_HEIGHT + 200 # Spawn below bottom edge
        elif side == 'left':
            x = 0 - 200 # Spawn left of left edge
            y = random.uniform(0, WORLD_HEIGHT - TITAN_SPRITE_DATA['frame_height'] * TITAN_SPRITE_DATA['scale'])
        else: # 'right'
            x = WORLD_WIDTH + 200 # Spawn right of right edge
            y = random.uniform(0, WORLD_HEIGHT - TITAN_SPRITE_DATA['frame_height'] * TITAN_SPRITE_DATA['scale'])

        # Ensure titan is not too close to the player's initial spawn point
        if (abs(x - player_pos_world[0]) > SCREEN_WIDTH / 2 or
            abs(y - player_pos_world[1]) > SCREEN_HEIGHT / 2):
            break
    
    return Titan(x, y, assets['beast_titan_sprite'])

def update_game_info(player_obj, wave_num, titans_remaining):
    """Updates the HUD with player stats and game info."""
    # HUD background
    hud_height = 80
    hud_surface = pygame.Surface((SCREEN_WIDTH, hud_height), pygame.SRCALPHA)
    hud_surface.fill((0, 0, 0, 200))
    screen.blit(hud_surface, (0, 0))

    font_medium = pygame.font.Font(None, 32)
    font_small = pygame.font.Font(None, 24)

    # Health bar
    health_percentage = player_obj.health / player_obj.max_health
    health_bar_width = 200
    health_bar_height = 20
    health_bar_x = 20
    health_bar_y = 15

    # Health bar background
    pygame.draw.rect(screen, DARK_RED, (health_bar_x, health_bar_y, health_bar_width, health_bar_height))
    # Health bar fill
    pygame.draw.rect(screen, RED, (health_bar_x, health_bar_y, health_bar_width * health_percentage, health_bar_height))
    # Health bar border
    pygame.draw.rect(screen, WHITE, (health_bar_x, health_bar_y, health_bar_width, health_bar_height), 2)

    # Health text
    health_text = font_small.render(f"HP: {player_obj.health}/{player_obj.max_health}", True, WHITE)
    screen.blit(health_text, (health_bar_x, health_bar_y + 25))

    # Gas bar (ODM Gear)
    gas_percentage = player_obj.gas_amount / player_obj.max_gas
    gas_bar_x = 20
    gas_bar_y = 50

    # Gas bar background
    pygame.draw.rect(screen, (50, 50, 50), (gas_bar_x, gas_bar_y, health_bar_width, health_bar_height))
    # Gas bar fill
    pygame.draw.rect(screen, BLUE, (gas_bar_x, gas_bar_y, health_bar_width * gas_percentage, health_bar_height))
    # Gas bar border
    pygame.draw.rect(screen, WHITE, (gas_bar_x, gas_bar_y, health_bar_width, health_bar_height), 2)

    # Gas text
    gas_text = font_small.render(f"Gas: {int(player_obj.gas_amount)}/{player_obj.max_gas}", True, WHITE)
    screen.blit(gas_text, (gas_bar_x + health_bar_width + 10, gas_bar_y + 2))

    # Score and stats (top right)
    score_text = font_medium.render(f"Score: {player_obj.score}", True, WHITE)
    wave_text = font_medium.render(f"Wave: {wave_num}", True, YELLOW)
    level_text = font_medium.render(f"Level: {player_obj.level}", True, GREEN)

    score_rect = score_text.get_rect(topright=(SCREEN_WIDTH - 20, 10))
    wave_rect = wave_text.get_rect(topright=(SCREEN_WIDTH - 20, 35))
    level_rect = level_text.get_rect(topright=(SCREEN_WIDTH - 200, 10))

    screen.blit(score_text, score_rect)
    screen.blit(wave_text, wave_rect)
    screen.blit(level_text, level_rect)

    # Combo counter
    if player_obj.combo_count > 1:
        combo_text = font_medium.render(f"COMBO x{player_obj.combo_count}!", True, GOLD)
        combo_rect = combo_text.get_rect(center=(SCREEN_WIDTH/2, 100))
        screen.blit(combo_text, combo_rect)

    # Experience bar (bottom of screen)
    if player_obj.level < 10:  # Max level cap
        exp_bar_width = SCREEN_WIDTH - 40
        exp_bar_height = 10
        exp_bar_x = 20
        exp_bar_y = SCREEN_HEIGHT - 30

        exp_percentage = player_obj.experience / player_obj.experience_to_next_level

        # Experience bar background
        pygame.draw.rect(screen, (50, 50, 50), (exp_bar_x, exp_bar_y, exp_bar_width, exp_bar_height))
        # Experience bar fill
        pygame.draw.rect(screen, GOLD, (exp_bar_x, exp_bar_y, exp_bar_width * exp_percentage, exp_bar_height))
        # Experience bar border
        pygame.draw.rect(screen, WHITE, (exp_bar_x, exp_bar_y, exp_bar_width, exp_bar_height), 1)

        # Experience text
        exp_text = font_small.render(f"EXP: {player_obj.experience}/{player_obj.experience_to_next_level}", True, WHITE)
        screen.blit(exp_text, (exp_bar_x, exp_bar_y - 20))

def play_sound(sound_key):
    """Play a sound effect."""
    if assets.get(sound_key) and assets[sound_key]:
        try:
            assets[sound_key].play()
        except pygame.error:
            pass

def play_attack_sound():
    """Play player attack sound."""
    play_sound('hit_sound')

def play_hit_sound():
    """Play titan hit sound."""
    play_sound('beast_hit_sound')

def play_player_hit_sound():
    """Play player getting hit sound."""
    play_sound('hit_sound')

def play_game_over_sound():
    """Stop all music for game over."""
    stop_music()

# --- Menu System ---
def draw_menu():
    """Draw the main menu."""
    screen.fill(BLACK)

    # Background
    loading_image = assets.get('loading_screen')
    if loading_image:
        loading_image = pygame.transform.scale(loading_image, (SCREEN_WIDTH, SCREEN_HEIGHT))
        screen.blit(loading_image, (0, 0))

    # Semi-transparent overlay
    overlay = pygame.Surface((SCREEN_WIDTH, SCREEN_HEIGHT))
    overlay.set_alpha(128)
    overlay.fill(BLACK)
    screen.blit(overlay, (0, 0))

    # Title
    font_large = pygame.font.Font(None, 100)
    title_text = font_large.render("ATTACK ON TITAN", True, RED)
    subtitle_text = font_large.render("SURVIVE", True, GOLD)

    title_rect = title_text.get_rect(center=(SCREEN_WIDTH/2, 150))
    subtitle_rect = subtitle_text.get_rect(center=(SCREEN_WIDTH/2, 220))

    screen.blit(title_text, title_rect)
    screen.blit(subtitle_text, subtitle_rect)

    # Character selection
    font_medium = pygame.font.Font(None, 50)
    char_text = font_medium.render("Choose Your Character:", True, WHITE)
    char_rect = char_text.get_rect(center=(SCREEN_WIDTH/2, 320))
    screen.blit(char_text, char_rect)

    # Character buttons
    levi_color = GOLD if selected_character == 'levi' else WHITE
    mikasa_color = GOLD if selected_character == 'mikasa' else WHITE

    levi_text = font_medium.render("LEVI", True, levi_color)
    mikasa_text = font_medium.render("MIKASA", True, mikasa_color)

    levi_rect = levi_text.get_rect(center=(SCREEN_WIDTH/2 - 150, 400))
    mikasa_rect = mikasa_text.get_rect(center=(SCREEN_WIDTH/2 + 150, 400))

    # Character selection backgrounds
    if selected_character == 'levi':
        pygame.draw.rect(screen, DARK_RED, levi_rect.inflate(20, 10), border_radius=5)
    if selected_character == 'mikasa':
        pygame.draw.rect(screen, DARK_RED, mikasa_rect.inflate(20, 10), border_radius=5)

    screen.blit(levi_text, levi_rect)
    screen.blit(mikasa_text, mikasa_rect)

    # Start button
    start_text = font_medium.render("PRESS SPACE TO START", True, GREEN)
    start_rect = start_text.get_rect(center=(SCREEN_WIDTH/2, 500))
    screen.blit(start_text, start_rect)

    # Instructions
    font_small = pygame.font.Font(None, 30)
    instructions = [
        "WASD - Move",
        "Left Click - Grapple (ODM Gear)",
        "Right Click - Release Grapple",
        "Space - Attack",
        "ESC - Pause"
    ]

    for i, instruction in enumerate(instructions):
        inst_text = font_small.render(instruction, True, WHITE)
        inst_rect = inst_text.get_rect(center=(SCREEN_WIDTH/2, 580 + i * 25))
        screen.blit(inst_text, inst_rect)

    pygame.display.flip()
    return levi_rect, mikasa_rect

# --- Main Game Loop Functions ---
def run_loading_screen():
    """Displays the loading screen while assets load."""
    loading_image = assets.get('loading_screen')
    if loading_image:
        loading_image = pygame.transform.scale(loading_image, (SCREEN_WIDTH, SCREEN_HEIGHT))
        screen.blit(loading_image, (0, 0))
    else:
        screen.fill(BLACK)
        font = pygame.font.Font(None, 74)
        text_surf = font.render("Loading...", True, WHITE)
        text_rect = text_surf.get_rect(center=(SCREEN_WIDTH / 2, SCREEN_HEIGHT / 2))
        screen.blit(text_surf, text_rect)

    pygame.display.flip()

def game_over_screen(player_score, wave_num, total_kills_count):
    """Displays the game over screen."""
    play_game_over_sound()
    screen.fill(BLACK)

    # Background
    loading_image = assets.get('loading_screen')
    if loading_image:
        loading_image = pygame.transform.scale(loading_image, (SCREEN_WIDTH, SCREEN_HEIGHT))
        screen.blit(loading_image, (0, 0))

    # Semi-transparent overlay
    overlay = pygame.Surface((SCREEN_WIDTH, SCREEN_HEIGHT))
    overlay.set_alpha(180)
    overlay.fill(BLACK)
    screen.blit(overlay, (0, 0))

    font_large = pygame.font.Font(None, 100)
    font_medium = pygame.font.Font(None, 50)
    font_small = pygame.font.Font(None, 40)

    game_over_text = font_large.render("HUMANITY FELL", True, RED)

    # Stats
    score_text = font_medium.render(f"Final Score: {player_score}", True, WHITE)
    wave_text = font_medium.render(f"Wave Reached: {wave_num}", True, WHITE)
    kills_text = font_medium.render(f"Titans Slain: {total_kills_count}", True, WHITE)

    # Buttons
    restart_text = font_small.render("R - Restart", True, GREEN)
    menu_text = font_small.render("M - Main Menu", True, YELLOW)
    quit_text = font_small.render("ESC - Quit", True, RED)

    # Positioning
    game_over_rect = game_over_text.get_rect(center=(SCREEN_WIDTH / 2, 200))
    score_rect = score_text.get_rect(center=(SCREEN_WIDTH / 2, 320))
    wave_rect = wave_text.get_rect(center=(SCREEN_WIDTH / 2, 370))
    kills_rect = kills_text.get_rect(center=(SCREEN_WIDTH / 2, 420))

    restart_rect = restart_text.get_rect(center=(SCREEN_WIDTH / 2, 520))
    menu_rect = menu_text.get_rect(center=(SCREEN_WIDTH / 2, 560))
    quit_rect = quit_text.get_rect(center=(SCREEN_WIDTH / 2, 600))

    screen.blit(game_over_text, game_over_rect)
    screen.blit(score_text, score_rect)
    screen.blit(wave_text, wave_rect)
    screen.blit(kills_text, kills_rect)
    screen.blit(restart_text, restart_rect)
    screen.blit(menu_text, menu_rect)
    screen.blit(quit_text, quit_rect)

    pygame.display.flip()

def pause_screen():
    """Displays the pause screen."""
    # Semi-transparent overlay
    overlay = pygame.Surface((SCREEN_WIDTH, SCREEN_HEIGHT))
    overlay.set_alpha(128)
    overlay.fill(BLACK)
    screen.blit(overlay, (0, 0))

    font_large = pygame.font.Font(None, 100)
    font_medium = pygame.font.Font(None, 50)

    pause_text = font_large.render("PAUSED", True, WHITE)
    resume_text = font_medium.render("ESC - Resume", True, GREEN)
    menu_text = font_medium.render("M - Main Menu", True, YELLOW)

    pause_rect = pause_text.get_rect(center=(SCREEN_WIDTH / 2, SCREEN_HEIGHT / 2 - 50))
    resume_rect = resume_text.get_rect(center=(SCREEN_WIDTH / 2, SCREEN_HEIGHT / 2 + 50))
    menu_rect = menu_text.get_rect(center=(SCREEN_WIDTH / 2, SCREEN_HEIGHT / 2 + 100))

    screen.blit(pause_text, pause_rect)
    screen.blit(resume_text, resume_rect)
    screen.blit(menu_text, menu_rect)

    pygame.display.flip()

def reset_game():
    """Resets all game variables for a new game."""
    global player, titans, titan_spawn_timer, camera_x, camera_y
    global wave_number, titans_killed_this_wave, titans_needed_for_next_wave, total_kills

    # Choose sprite based on selected character
    sprite_key = 'player_sprite' if selected_character == 'levi' else 'mikasa_sprite'
    player = Player(WORLD_WIDTH / 2, WORLD_HEIGHT / 2, assets[sprite_key])
    titans = []
    titan_spawn_timer = 0
    camera_x, camera_y = 0, 0

    # Reset wave system
    wave_number = 1
    titans_killed_this_wave = 0
    titans_needed_for_next_wave = 5
    total_kills = 0

    # Spawn initial titans
    for _ in range(3):
        titans.append(spawn_titan((player.x, player.y)))

    # Start game music
    music_tracks = ['game_music_1', 'game_music_2', 'game_music_3']
    selected_track = random.choice(music_tracks)
    if assets.get(selected_track):
        play_music(assets[selected_track])

# --- Main Game Loop ---
def run_game():
    global game_state, camera_x, camera_y, titan_spawn_timer, selected_character
    global wave_number, titans_killed_this_wave, titans_needed_for_next_wave, total_kills

    clock = pygame.time.Clock()

    # Initial loading
    global assets
    assets = load_assets()

    # Start menu music
    if assets.get('menu_music'):
        play_music(assets['menu_music'])

    running = True
    levi_rect = mikasa_rect = None

    while running:
        if game_state == 'MENU':
            dt = clock.tick(MENU_FPS)
        else:
            dt = clock.tick(FPS)

        # Event handling
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                running = False

            elif event.type == pygame.KEYDOWN:
                if game_state == 'MENU':
                    if event.key == pygame.K_SPACE:
                        game_state = 'PLAYING'
                        reset_game()
                    elif event.key == pygame.K_LEFT or event.key == pygame.K_a:
                        selected_character = 'levi'
                    elif event.key == pygame.K_RIGHT or event.key == pygame.K_d:
                        selected_character = 'mikasa'

                elif game_state == 'PLAYING':
                    if event.key == pygame.K_ESCAPE:
                        game_state = 'PAUSED'

                elif game_state == 'PAUSED':
                    if event.key == pygame.K_ESCAPE:
                        game_state = 'PLAYING'
                    elif event.key == pygame.K_m:
                        game_state = 'MENU'
                        if assets.get('menu_music'):
                            play_music(assets['menu_music'])

                elif game_state == 'GAME_OVER':
                    if event.key == pygame.K_r:
                        game_state = 'PLAYING'
                        reset_game()
                    elif event.key == pygame.K_m:
                        game_state = 'MENU'
                        if assets.get('menu_music'):
                            play_music(assets['menu_music'])
                    elif event.key == pygame.K_ESCAPE:
                        running = False

            elif event.type == pygame.MOUSEBUTTONDOWN:
                if game_state == 'MENU' and levi_rect and mikasa_rect:
                    if levi_rect.collidepoint(event.pos):
                        selected_character = 'levi'
                    elif mikasa_rect.collidepoint(event.pos):
                        selected_character = 'mikasa'

        # Game state handling
        if game_state == 'MENU':
            levi_rect, mikasa_rect = draw_menu()

        elif game_state == 'LOADING':
            run_loading_screen()

        elif game_state == 'PLAYING':
            keys = pygame.key.get_pressed()
            mouse_pos = pygame.mouse.get_pos()
            mouse_pressed = pygame.mouse.get_pressed()

            # --- Update ---
            player.update(dt, keys, mouse_pos, mouse_pressed)

            # Update Camera to follow player (centered)
            camera_x = player.x - SCREEN_WIDTH / 2
            camera_y = player.y - SCREEN_HEIGHT / 2

            # Clamp camera to world bounds
            camera_x = max(0, min(WORLD_WIDTH - SCREEN_WIDTH, camera_x))
            camera_y = max(0, min(WORLD_HEIGHT - SCREEN_HEIGHT, camera_y))

            # Wave system - spawn more titans based on wave
            max_titans = min(15, 5 + wave_number * 2)
            titan_spawn_timer += dt
            spawn_interval = max(1000, 3000 - wave_number * 200)  # Faster spawning each wave

            if titan_spawn_timer >= spawn_interval and len(titans) < max_titans:
                titans.append(spawn_titan((player.x, player.y)))
                titan_spawn_timer = 0

            # Update Titans
            for titan in titans:
                titan.update(dt, (player.x, player.y))

            # Update particles
            update_particles(dt)

            # Collision Detection
            # Player Attack vs. Titans
            if player.is_attacking:
                attack_range = 80 + (player.combo_count * 10)  # Larger range with combo
                player_attack_rect = player.rect.inflate(attack_range, attack_range)

                for titan in titans:
                    if check_collision(player_attack_rect, titan.rect):
                        if not titan.hit_by_player:
                            damage = player.attack_damage + (player.combo_count * 5)
                            titan.health -= damage
                            titan.hit_by_player = True
                            play_hit_sound()
                            create_hit_particles(titan.x + titan.width/2, titan.y + titan.height/2)

                            if titan.health <= 0:
                                # Calculate score based on combo and wave
                                base_score = 100
                                combo_bonus = player.combo_count * 25
                                wave_bonus = wave_number * 10
                                total_score = base_score + combo_bonus + wave_bonus

                                player.score += total_score
                                player.experience += 20 + wave_number * 5
                                titans_killed_this_wave += 1
                                total_kills += 1

                                # Level up check
                                if player.experience >= player.experience_to_next_level and player.level < 10:
                                    player.level += 1
                                    player.experience = 0
                                    player.experience_to_next_level += 50
                                    player.max_health += 20
                                    player.health = player.max_health  # Full heal on level up
                                    player.attack_damage += 5
                                    play_sound('jump_sound')  # Level up sound
                    else:
                        titan.hit_by_player = False

            # Titans vs. Player
            for titan in titans:
                if check_collision(player.rect, titan.rect):
                    if titan.attack_timer >= titan.attack_cooldown:
                        damage = titan.attack_damage + (wave_number - 1) * 2  # Stronger titans each wave
                        if player.take_damage(damage):
                            game_state = 'GAME_OVER'
                        titan.attack_timer = 0

            # Remove dead titans
            titans[:] = [titan for titan in titans if titan.health > 0]

            # Wave progression
            if titans_killed_this_wave >= titans_needed_for_next_wave:
                wave_number += 1
                titans_killed_this_wave = 0
                titans_needed_for_next_wave += 3  # More titans needed each wave

                # Bonus for completing wave
                player.score += wave_number * 50
                player.experience += wave_number * 10

                # Heal player slightly
                player.health = min(player.max_health, player.health + 20)

            # --- Render ---
            screen.fill(BLACK) # Clear screen

            # Parallax Backgrounds
            # Use 'shiganshina' as the furthest background (most static)
            if assets.get('shiganshina'):
                # Calculate offsets for parallax. Larger divisor means less movement.
                parallax_offset_shiganshina_x = -camera_x * 0.1
                parallax_offset_shiganshina_y = -camera_y * 0.1
                
                # Tile the background
                # Assuming shiganshina is a smaller tile, we'll tile it
                shiganshina_img = assets['shiganshina']
                shiganshina_width = shiganshina_img.get_width()
                shiganshina_height = shiganshina_img.get_height()

                # Calculate start and end indices for tiling based on camera and world size
                start_x = int((camera_x * 0.1) / shiganshina_width) * shiganshina_width
                start_y = int((camera_y * 0.1) / shiganshina_height) * shiganshina_height

                for x in range(int(start_x), int(start_x + SCREEN_WIDTH + shiganshina_width), shiganshina_width):
                    for y in range(int(start_y), int(start_y + SCREEN_HEIGHT + shiganshina_height), shiganshina_height):
                        screen.blit(shiganshina_img, (x - camera_x * 0.1, y - camera_y * 0.1))

            # AOT Background buildings (next layer) - Assuming this is a single, large image for parallax
            if assets.get('bg_buildings'):
                parallax_offset_buildings_x = -camera_x * 0.3
                parallax_offset_buildings_y = -camera_y * 0.3
                bg_buildings_img = assets['bg_buildings']
                # Scale to fit the world width, maintaining aspect ratio
                scaled_height = int(bg_buildings_img.get_height() * WORLD_WIDTH / bg_buildings_img.get_width())
                bg_buildings_scaled = pygame.transform.scale(bg_buildings_img, (WORLD_WIDTH, scaled_height))
                
                # Center the background buildings vertically in the world
                bg_y_offset = (WORLD_HEIGHT - scaled_height) / 2 
                screen.blit(bg_buildings_scaled, (parallax_offset_buildings_x, bg_y_offset + parallax_offset_buildings_y))


            # Midground (closer layer) - Similar to buildings, assume large image
            if assets.get('midground'):
                parallax_offset_midground_x = -camera_x * 0.5
                parallax_offset_midground_y = -camera_y * 0.5
                midground_img = assets['midground']
                scaled_height = int(midground_img.get_height() * WORLD_WIDTH / midground_img.get_width())
                midground_scaled = pygame.transform.scale(midground_img, (WORLD_WIDTH, scaled_height))
                
                mid_y_offset = (WORLD_HEIGHT - scaled_height) / 2
                screen.blit(midground_scaled, (parallax_offset_midground_x, mid_y_offset + parallax_offset_midground_y))
            
            # Foreground (closest, most dynamic) - Similar to buildings, assume large image
            if assets.get('foreground'):
                parallax_offset_foreground_x = -camera_x * 0.7
                parallax_offset_foreground_y = -camera_y * 0.7
                foreground_img = assets['foreground']
                scaled_height = int(foreground_img.get_height() * WORLD_WIDTH / foreground_img.get_width())
                foreground_scaled = pygame.transform.scale(foreground_img, (WORLD_WIDTH, scaled_height))
                
                fore_y_offset = (WORLD_HEIGHT - scaled_height) / 2
                screen.blit(foreground_scaled, (parallax_offset_foreground_x, fore_y_offset + parallax_offset_foreground_y))


            # Draw Titans (relative to camera)
            for titan in titans:
                # If player is invulnerable, make them slightly transparent for visual feedback
                if player.invulnerable and player.invulnerable_timer % 200 < 100: # Simple blinking effect
                    titan_surface = titan.image.copy()
                    titan_surface.set_alpha(100) # Semi-transparent
                    screen.blit(titan_surface, (titan.x - camera_x, titan.y - camera_y))
                else:
                    screen.blit(titan.image, (titan.x - camera_x, titan.y - camera_y))

            # Draw Player (relative to camera)
            # Flip player image if facing left
            player_image = player.image
            if not player.facing_right:
                player_image = pygame.transform.flip(player_image, True, False) # Flip horizontally

            # Blinking effect for player invulnerability
            if player.invulnerable and player.invulnerable_timer % 200 < 100:
                player_image.set_alpha(100) # Semi-transparent
            else:
                player_image.set_alpha(255) # Fully opaque

            screen.blit(player_image, (player.x - camera_x, player.y - camera_y))

            # Draw grappling hook line
            if player.is_grappling:
                start_pos = (player.x + player.width/2 - camera_x, player.y + player.height/2 - camera_y)
                end_pos = (player.grapple_target_x - camera_x, player.grapple_target_y - camera_y)
                pygame.draw.line(screen, BLUE, start_pos, end_pos, 3)

                # Draw grapple target indicator
                pygame.draw.circle(screen, YELLOW, (int(end_pos[0]), int(end_pos[1])), 8, 2)

            # Draw particles
            draw_particles(screen, camera_x, camera_y)

            # Draw UI
            update_game_info(player, wave_number, len(titans))

            pygame.display.flip()

        elif game_state == 'PAUSED':
            pause_screen()

        elif game_state == 'GAME_OVER':
            game_over_screen(player.score, wave_number, total_kills)

    pygame.quit()
    sys.exit()

if __name__ == "__main__":
    run_game()
