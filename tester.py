import pygame
import random
import os
import sys

# --- Pygame Initialization ---
pygame.init()
pygame.mixer.init() # Initialize the mixer for sound effects

# --- Game Constants ---
SCREEN_WIDTH = 1000
SCREEN_HEIGHT = 700
FPS = 60 # Frames per second

# Set up the display
screen = pygame.display.set_mode((SCREEN_WIDTH, SCREEN_HEIGHT))
pygame.display.set_caption("Attack on Titan: Survive")

# Colors
WHITE = (255, 255, 255)
BLACK = (0, 0, 0)
RED = (255, 0, 0)
GREEN = (0, 255, 0)
GRAY = (150, 150, 150)

# Game World Dimensions (arbitrary larger than canvas for scrolling)
WORLD_WIDTH = 4000
WORLD_HEIGHT = 4000

# --- Asset Paths (Update these if your files are in a different directory) ---
ASSET_DIR = "" # Current directory, or specify a path like "assets/"

IMAGE_PATHS = {
    'loading_screen': os.path.join(ASSET_DIR, 'AOT_loadingscreen.png'),
    'player_sprite': os.path.join(ASSET_DIR, 'levi.png'),
    'beast_titan_sprite': os.path.join(ASSET_DIR, 'attack-on-titan-beast-titan-1.png'),
    'bg_buildings': os.path.join(ASSET_DIR, 'AOT Background buildings.png'),
    'midground': os.path.join(ASSET_DIR, 'midground.png'),
    'foreground': os.path.join(ASSET_DIR, 'foreground.png'),
    'shiganshina': os.path.join(ASSET_DIR, 'shiganshina.png')
}

# Sound Paths (Uncomment and provide actual sound files if you want to use them)
# SOUND_PATHS = {
#     'attack_sound': os.path.join(ASSET_DIR, 'attack.wav'),
#     'hit_sound': os.path.join(ASSET_DIR, 'hit.wav'),
#     'player_hit_sound': os.path.join(ASSET_DIR, 'player_hit.wav'),
#     'game_over_sound': os.path.join(ASSET_DIR, 'game_over.wav'),
# }

# --- Asset Loading ---
def load_assets():
    """Loads all game images and sounds."""
    assets = {}
    for key, path in IMAGE_PATHS.items():
        try:
            image = pygame.image.load(path).convert_alpha()
            assets[key] = image
            print(f"Loaded image: {path}")
        except pygame.error as e:
            print(f"Error loading image {path}: {e}")
            # Load a placeholder if image fails to load
            placeholder_surface = pygame.Surface((100, 100))
            placeholder_surface.fill(GRAY)
            font = pygame.font.Font(None, 20)
            text_surf = font.render(key, True, BLACK)
            text_rect = text_surf.get_rect(center=(50, 50))
            placeholder_surface.blit(text_surf, text_rect)
            assets[key] = placeholder_surface

    # Load sounds (uncomment if you have sound files)
    # for key, path in SOUND_PATHS.items():
    #     try:
    #         sound = pygame.mixer.Sound(path)
    #         assets[key] = sound
    #         print(f"Loaded sound: {path}")
    #     except pygame.error as e:
    #         print(f"Error loading sound {path}: {e}")
    #         assets[key] = None # Set to None if sound fails to load

    return assets

# --- Game State ---
game_state = 'LOADING' # LOADING, PLAYING, GAME_OVER
assets = {} # Will store loaded images and sounds
camera_x, camera_y = 0, 0 # Camera position

# --- Sprite Sheet Data (Adjust based on your actual sprite sheets) ---
# Levi sprite sheet: (width: 320, height: 160) - assuming each frame is 61x58 based on user input
PLAYER_SPRITE_DATA = {
    'frame_width': 61,  # Updated based on user input
    'frame_height': 58, # Updated based on user input
    'scale': 1.5, # Scale up the player sprite
    'animations': {
        'idle': [(0, 0)],
        'walk_right': [(0, 0), (0, 1), (0, 2), (0, 3)],
        'walk_left': [(0, 4), (0, 5), (0, 6), (0, 7)], # Assuming these frames exist for left walk
        'attack': [ # Custom attack frames from your levi.png
            (1, 0), (1, 1), (1, 2), (1, 3), (1, 4), (1, 5),
            (2, 0), (2, 1), (2, 2), (2, 3)
        ]
    }
}

# Beast Titan sprite sheet: (width: 1408, height: 96) - assuming each frame is 96x96
TITAN_SPRITE_DATA = {
    'frame_width': 96,
    'frame_height': 96,
    'scale': 2.0, # Scale up the titan sprite
    'animations': {
        'idle': [(0, 0)],
        'walk': [(0, 0), (0, 1), (0, 2), (0, 3), (0, 4)],
        'attack': [(0, 5), (0, 6), (0, 7), (0, 8), (0, 9), (0, 10), (0, 11)]
    }
}

# --- Game Entities ---
class Entity(pygame.sprite.Sprite):
    """Base class for player and enemies."""
    def __init__(self, x, y, width, height, sprite_sheet_img, sprite_data):
        super().__init__()
        self.x = x
        self.y = y
        self.original_width = width
        self.original_height = height
        self.sprite_sheet = sprite_sheet_img
        self.sprite_data = sprite_data
        self.current_animation = 'idle'
        self.frame_index = 0
        self.animation_timer = 0
        self.frame_delay = 100 # milliseconds per frame (10 FPS default)

        # Scale based on sprite_data
        self.width = int(self.original_width * self.sprite_data['scale'])
        self.height = int(self.original_height * self.sprite_data['scale'])

        # Create a surface for the current frame
        self.image = pygame.Surface((self.width, self.height), pygame.SRCALPHA) # SRCALPHA for transparency
        self.rect = self.image.get_rect(topleft=(self.x, self.y)) # Initial rect

    def get_frame(self):
        """Extracts and scales the current frame from the sprite sheet."""
        if self.current_animation not in self.sprite_data['animations'] or not self.sprite_data['animations'][self.current_animation]:
            self.current_animation = 'idle' # Fallback if animation not found or empty
            if 'idle' not in self.sprite_data['animations'] or not self.sprite_data['animations']['idle']:
                 return pygame.Surface((self.width, self.height), pygame.SRCALPHA) # Return empty if no idle frames

        frames = self.sprite_data['animations'][self.current_animation]
        
        row, col = frames[self.frame_index]
        frame_rect = pygame.Rect(
            col * self.sprite_data['frame_width'],
            row * self.sprite_data['frame_height'],
            self.sprite_data['frame_width'],
            self.sprite_data['frame_height']
        )
        # Create a new surface to draw the frame onto, ensuring it's the target size
        frame_surface = pygame.Surface((self.sprite_data['frame_width'], self.sprite_data['frame_height']), pygame.SRCALPHA)
        frame_surface.blit(self.sprite_sheet, (0, 0), frame_rect)
        return pygame.transform.scale(frame_surface, (self.width, self.height))

    def update_animation(self, dt):
        """Updates the animation frame based on delta time."""
        if self.current_animation not in self.sprite_data['animations'] or not self.sprite_data['animations'][self.current_animation]:
            self.current_animation = 'idle' # Fallback
            if 'idle' not in self.sprite_data['animations'] or not self.sprite_data['animations']['idle']:
                return # Can't animate if no valid animation or idle frames

        self.animation_timer += dt
        if self.animation_timer >= self.frame_delay:
            self.frame_index = (self.frame_index + 1) % len(self.sprite_data['animations'][self.current_animation])
            self.animation_timer = 0
        self.image = self.get_frame() # Update the image surface

class Player(Entity):
    def __init__(self, x, y, sprite_sheet_img):
        super().__init__(x, y, PLAYER_SPRITE_DATA['frame_width'], PLAYER_SPRITE_DATA['frame_height'],
                         sprite_sheet_img, PLAYER_SPRITE_DATA)
        self.speed = 200 # Pixels per second
        self.health = 100
        self.max_health = 100
        self.score = 0
        self.is_attacking = False
        self.attack_duration = 300 # ms
        self.attack_timer = 0
        self.attack_damage = 25
        self.facing_right = True
        self.invulnerable = False
        self.invulnerable_timer = 0
        self.invulnerable_duration = 1000 # 1 second invulnerability

    def update(self, dt, keys):
        """Updates player position, animation, and state."""
        self.update_animation(dt) # Always update animation, even if idle

        move_x, move_y = 0, 0
        moving = False
        if keys[pygame.K_w] or keys[pygame.K_UP]:
            move_y -= 1
            moving = True
        if keys[pygame.K_s] or keys[pygame.K_DOWN]:
            move_y += 1
            moving = True
        if keys[pygame.K_a] or keys[pygame.K_LEFT]:
            move_x -= 1
            moving = True
            self.facing_right = False
        if keys[pygame.K_d] or keys[pygame.K_RIGHT]:
            move_x += 1
            moving = True
            self.facing_right = True

        # Normalize diagonal movement
        if move_x != 0 and move_y != 0:
            factor = (self.speed * dt / 1000) / (2**0.5)
            self.x += move_x * factor
            self.y += move_y * factor
        else:
            self.x += move_x * self.speed * dt / 1000
            self.y += move_y * self.speed * dt / 1000

        # Clamp player position within world bounds
        self.x = max(0, min(WORLD_WIDTH - self.width, self.x))
        self.y = max(0, min(WORLD_HEIGHT - self.height, self.y))

        self.rect.topleft = (self.x, self.y)

        # Player Attack Logic
        if self.is_attacking:
            self.attack_timer += dt
            if self.attack_timer >= self.attack_duration:
                self.is_attacking = False
                self.attack_timer = 0
                self.current_animation = 'idle' # Reset after attack
                self.frame_index = 0
        elif keys[pygame.K_SPACE] and not self.is_attacking:
            self.is_attacking = True
            self.attack_timer = 0
            self.current_animation = 'attack'
            self.frame_index = 0 # Start attack animation from first frame
            play_attack_sound()
        elif moving:
            self.current_animation = 'walk_right' if self.facing_right else 'walk_left'
        else:
            self.current_animation = 'idle'
            self.frame_index = 0 # Reset frame if idle

        # Player Invulnerability
        if self.invulnerable:
            self.invulnerable_timer += dt
            if self.invulnerable_timer >= self.invulnerable_duration:
                self.invulnerable = False
                self.invulnerable_timer = 0

    def take_damage(self, damage):
        if not self.invulnerable:
            self.health -= damage
            play_player_hit_sound()
            self.invulnerable = True
            self.invulnerable_timer = 0
            if self.health <= 0:
                self.health = 0
                return True # Player is dead
        return False

class Titan(Entity):
    def __init__(self, x, y, sprite_sheet_img):
        super().__init__(x, y, TITAN_SPRITE_DATA['frame_width'], TITAN_SPRITE_DATA['frame_height'],
                         sprite_sheet_img, TITAN_SPRITE_DATA)
        self.speed = random.randint(50, 100) # Pixels per second
        self.health = 100
        self.attack_damage = 10
        self.attack_cooldown = 1000 # ms
        self.attack_timer = 0
        self.hit_by_player = False # To ensure only one hit per player attack

    def update(self, dt, player_pos):
        """Updates titan position, animation, and state."""
        self.update_animation(dt)

        # Move towards player
        dx = player_pos[0] - self.x
        dy = player_pos[1] - self.y
        dist = (dx**2 + dy**2)**0.5

        if dist > 0: # Avoid division by zero
            self.x += (dx / dist) * self.speed * dt / 1000
            self.y += (dy / dist) * self.speed * dt / 1000

        self.rect.topleft = (self.x, self.y)

        # Titan Animation (simple walk, or attack if very close)
        if dist < 100: # If close enough to attack
            self.current_animation = 'attack'
        else:
            self.current_animation = 'walk'

        # Attack cooldown
        self.attack_timer += dt

# --- Game Functions ---
def check_collision(rect1, rect2):
    """Checks for simple AABB collision between two pygame Rects."""
    return rect1.colliderect(rect2)

def spawn_titan(player_pos_world):
    """Spawns a titan at a random location far from the player."""
    while True:
        # Spawn outside visible screen area, but within world bounds
        side = random.choice(['top', 'bottom', 'left', 'right'])
        if side == 'top':
            x = random.uniform(0, WORLD_WIDTH - TITAN_SPRITE_DATA['frame_width'] * TITAN_SPRITE_DATA['scale'])
            y = 0 - 200 # Spawn above top edge
        elif side == 'bottom':
            x = random.uniform(0, WORLD_WIDTH - TITAN_SPRITE_DATA['frame_width'] * TITAN_SPRITE_DATA['scale'])
            y = WORLD_HEIGHT + 200 # Spawn below bottom edge
        elif side == 'left':
            x = 0 - 200 # Spawn left of left edge
            y = random.uniform(0, WORLD_HEIGHT - TITAN_SPRITE_DATA['frame_height'] * TITAN_SPRITE_DATA['scale'])
        else: # 'right'
            x = WORLD_WIDTH + 200 # Spawn right of right edge
            y = random.uniform(0, WORLD_HEIGHT - TITAN_SPRITE_DATA['frame_height'] * TITAN_SPRITE_DATA['scale'])

        # Ensure titan is not too close to the player's initial spawn point
        if (abs(x - player_pos_world[0]) > SCREEN_WIDTH / 2 or
            abs(y - player_pos_world[1]) > SCREEN_HEIGHT / 2):
            break
    
    return Titan(x, y, assets['beast_titan_sprite'])

def update_game_info(player_obj):
    """Updates the score and health displays."""
    health_text = f"Health: {player_obj.health}"
    score_text = f"Score: {player_obj.score}"

    # Get UI elements
    font = pygame.font.Font(None, 36)
    health_display = font.render(health_text, True, WHITE)
    score_display = font.render(score_text, True, WHITE)

    # Draw a semi-transparent background for the info bar
    info_bar_height = 50
    info_bar_surface = pygame.Surface((SCREEN_WIDTH, info_bar_height), pygame.SRCALPHA)
    info_bar_surface.fill((0, 0, 0, 180)) # Black with 180 alpha (out of 255)
    screen.blit(info_bar_surface, (0, 0))

    # Position text
    health_rect = health_display.get_rect(topleft=(20, 10))
    score_rect = score_display.get_rect(topright=(SCREEN_WIDTH - 20, 10))

    screen.blit(health_display, health_rect)
    screen.blit(score_display, score_rect)

def play_attack_sound():
    """Placeholder for player attack sound."""
    # if assets.get('attack_sound'):
    #     assets['attack_sound'].play()
    pass

def play_hit_sound():
    """Placeholder for titan hit sound."""
    # if assets.get('hit_sound'):
    #     assets['hit_sound'].play()
    pass

def play_player_hit_sound():
    """Placeholder for player getting hit sound."""
    # if assets.get('player_hit_sound'):
    #     assets['player_hit_sound'].play()
    pass

def play_game_over_sound():
    """Placeholder for game over sound."""
    # if assets.get('game_over_sound'):
    #     assets['game_over_sound'].play()
    pass

# --- Main Game Loop Functions ---
def run_loading_screen():
    """Displays the loading screen while assets load."""
    loading_image = assets.get('loading_screen')
    if loading_image:
        loading_image = pygame.transform.scale(loading_image, (SCREEN_WIDTH, SCREEN_HEIGHT))
        screen.blit(loading_image, (0, 0))
    else:
        screen.fill(BLACK)
        font = pygame.font.Font(None, 74)
        text_surf = font.render("Loading...", True, WHITE)
        text_rect = text_surf.get_rect(center=(SCREEN_WIDTH / 2, SCREEN_HEIGHT / 2))
        screen.blit(text_surf, text_rect)

    pygame.display.flip()

def game_over_screen(player_score):
    """Displays the game over screen."""
    play_game_over_sound()
    screen.fill(BLACK)
    
    font_large = pygame.font.Font(None, 100)
    font_medium = pygame.font.Font(None, 74)
    font_small = pygame.font.Font(None, 50)

    game_over_text = font_large.render("GAME OVER!", True, RED)
    score_text = font_medium.render(f"Score: {player_score}", True, WHITE)
    
    restart_button_text = font_small.render("Restart Game", True, WHITE)

    game_over_rect = game_over_text.get_rect(center=(SCREEN_WIDTH / 2, SCREEN_HEIGHT / 2 - 100))
    score_rect = score_text.get_rect(center=(SCREEN_WIDTH / 2, SCREEN_HEIGHT / 2))
    
    restart_button_rect = restart_button_text.get_rect(center=(SCREEN_WIDTH / 2, SCREEN_HEIGHT / 2 + 100))
    
    # Draw a background for the button to make it look clickable
    button_bg_rect = restart_button_rect.inflate(40, 20) # Add padding
    pygame.draw.rect(screen, (170, 0, 0), button_bg_rect, border_radius=15) # Darker red background
    pygame.draw.rect(screen, RED, button_bg_rect, 5, border_radius=15) # Red border

    screen.blit(game_over_text, game_over_rect)
    screen.blit(score_text, score_rect)
    screen.blit(restart_button_text, restart_button_rect)

    pygame.display.flip()
    return restart_button_rect # Return button rect for click detection

def reset_game():
    """Resets all game variables for a new game."""
    global player, titans, titan_spawn_timer, camera_x, camera_y
    player = Player(WORLD_WIDTH / 2, WORLD_HEIGHT / 2, assets['player_sprite'])
    titans = []
    titan_spawn_timer = 0
    camera_x, camera_y = 0, 0 # Reset camera
    
    # Spawn initial titans
    for _ in range(3):
        titans.append(spawn_titan((player.x, player.y)))

# --- Main Game Loop ---
def run_game():
    global game_state, camera_x, camera_y, titan_spawn_timer

    clock = pygame.time.Clock()
    
    # Initial loading
    global assets
    assets = load_assets()
    
    # After loading, set game state to playing and initialize entities
    game_state = 'PLAYING'
    reset_game()

    running = True
    while running:
        dt = clock.tick(FPS) # Delta time in milliseconds

        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                running = False
            if event.type == pygame.MOUSEBUTTONDOWN and game_state == 'GAME_OVER':
                # Check if restart button was clicked
                if restart_button_rect.collidepoint(event.pos):
                    game_state = 'PLAYING'
                    reset_game()

        if game_state == 'LOADING':
            run_loading_screen()
        elif game_state == 'PLAYING':
            keys = pygame.key.get_pressed()

            # --- Update ---
            player.update(dt, keys)

            # Update Camera to follow player (centered)
            camera_x = player.x - SCREEN_WIDTH / 2
            camera_y = player.y - SCREEN_HEIGHT / 2

            # Clamp camera to world bounds
            camera_x = max(0, min(WORLD_WIDTH - SCREEN_WIDTH, camera_x))
            camera_y = max(0, min(WORLD_HEIGHT - SCREEN_HEIGHT, camera_y))

            # Titan Spawning
            titan_spawn_timer += dt
            if titan_spawn_timer >= 3000 and len(titans) < 10: # Spawn every 3 seconds, max 10 titans
                titans.append(spawn_titan((player.x, player.y)))
                titan_spawn_timer = 0

            # Update Titans
            for titan in titans:
                titan.update(dt, (player.x, player.y))

            # Collision Detection
            # Player Attack vs. Titans
            if player.is_attacking:
                player_attack_rect = player.rect.inflate(50, 50) # Expand player rect slightly for attack hitbox
                for titan in titans:
                    if check_collision(player_attack_rect, titan.rect):
                        if not titan.hit_by_player: # Only hit once per attack
                            titan.health -= player.attack_damage
                            titan.hit_by_player = True
                            play_hit_sound()
                            if titan.health <= 0:
                                player.score += 100
                    else:
                        titan.hit_by_player = False # Reset hit status if not colliding

            # Titans vs. Player
            for titan in titans:
                if check_collision(player.rect, titan.rect):
                    if titan.attack_timer >= titan.attack_cooldown:
                        if player.take_damage(titan.attack_damage):
                            game_state = 'GAME_OVER' # Player died
                        titan.attack_timer = 0 # Reset titan attack cooldown

            # Remove dead titans
            titans[:] = [titan for titan in titans if titan.health > 0]

            # --- Render ---
            screen.fill(BLACK) # Clear screen

            # Parallax Backgrounds
            # Use 'shiganshina' as the furthest background (most static)
            if assets.get('shiganshina'):
                # Calculate offsets for parallax. Larger divisor means less movement.
                parallax_offset_shiganshina_x = -camera_x * 0.1
                parallax_offset_shiganshina_y = -camera_y * 0.1
                
                # Tile the background
                # Assuming shiganshina is a smaller tile, we'll tile it
                shiganshina_img = assets['shiganshina']
                shiganshina_width = shiganshina_img.get_width()
                shiganshina_height = shiganshina_img.get_height()

                # Calculate start and end indices for tiling based on camera and world size
                start_x = int((camera_x * 0.1) / shiganshina_width) * shiganshina_width
                start_y = int((camera_y * 0.1) / shiganshina_height) * shiganshina_height

                for x in range(int(start_x), int(start_x + SCREEN_WIDTH + shiganshina_width), shiganshina_width):
                    for y in range(int(start_y), int(start_y + SCREEN_HEIGHT + shiganshina_height), shiganshina_height):
                        screen.blit(shiganshina_img, (x - camera_x * 0.1, y - camera_y * 0.1))

            # AOT Background buildings (next layer) - Assuming this is a single, large image for parallax
            if assets.get('bg_buildings'):
                parallax_offset_buildings_x = -camera_x * 0.3
                parallax_offset_buildings_y = -camera_y * 0.3
                bg_buildings_img = assets['bg_buildings']
                # Scale to fit the world width, maintaining aspect ratio
                scaled_height = int(bg_buildings_img.get_height() * WORLD_WIDTH / bg_buildings_img.get_width())
                bg_buildings_scaled = pygame.transform.scale(bg_buildings_img, (WORLD_WIDTH, scaled_height))
                
                # Center the background buildings vertically in the world
                bg_y_offset = (WORLD_HEIGHT - scaled_height) / 2 
                screen.blit(bg_buildings_scaled, (parallax_offset_buildings_x, bg_y_offset + parallax_offset_buildings_y))


            # Midground (closer layer) - Similar to buildings, assume large image
            if assets.get('midground'):
                parallax_offset_midground_x = -camera_x * 0.5
                parallax_offset_midground_y = -camera_y * 0.5
                midground_img = assets['midground']
                scaled_height = int(midground_img.get_height() * WORLD_WIDTH / midground_img.get_width())
                midground_scaled = pygame.transform.scale(midground_img, (WORLD_WIDTH, scaled_height))
                
                mid_y_offset = (WORLD_HEIGHT - scaled_height) / 2
                screen.blit(midground_scaled, (parallax_offset_midground_x, mid_y_offset + parallax_offset_midground_y))
            
            # Foreground (closest, most dynamic) - Similar to buildings, assume large image
            if assets.get('foreground'):
                parallax_offset_foreground_x = -camera_x * 0.7
                parallax_offset_foreground_y = -camera_y * 0.7
                foreground_img = assets['foreground']
                scaled_height = int(foreground_img.get_height() * WORLD_WIDTH / foreground_img.get_width())
                foreground_scaled = pygame.transform.scale(foreground_img, (WORLD_WIDTH, scaled_height))
                
                fore_y_offset = (WORLD_HEIGHT - scaled_height) / 2
                screen.blit(foreground_scaled, (parallax_offset_foreground_x, fore_y_offset + parallax_offset_foreground_y))


            # Draw Titans (relative to camera)
            for titan in titans:
                # If player is invulnerable, make them slightly transparent for visual feedback
                if player.invulnerable and player.invulnerable_timer % 200 < 100: # Simple blinking effect
                    titan_surface = titan.image.copy()
                    titan_surface.set_alpha(100) # Semi-transparent
                    screen.blit(titan_surface, (titan.x - camera_x, titan.y - camera_y))
                else:
                    screen.blit(titan.image, (titan.x - camera_x, titan.y - camera_y))

            # Draw Player (relative to camera)
            # Flip player image if facing left
            player_image = player.image
            if not player.facing_right:
                player_image = pygame.transform.flip(player_image, True, False) # Flip horizontally

            # Blinking effect for player invulnerability
            if player.invulnerable and player.invulnerable_timer % 200 < 100:
                player_image.set_alpha(100) # Semi-transparent
            else:
                player_image.set_alpha(255) # Fully opaque

            screen.blit(player_image, (player.x - camera_x, player.y - camera_y))

            # Draw UI
            update_game_info(player)

            pygame.display.flip()

        elif game_state == 'GAME_OVER':
            restart_button_rect = game_over_screen(player.score) # Display game over screen and get button rect

    pygame.quit()
    sys.exit()

if __name__ == "__main__":
    run_game()
