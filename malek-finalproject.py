import pygame

pygame.init()

# SETUP THE GAME WINDOW
SCREEN_WIDTH = 1000
SCREEN_HEIGHT = 800
screen = pygame.display.set_mode([SCREEN_WIDTH, SCREEN_HEIGHT])
pygame.display.set_caption("The Rumbling")
clock = pygame.time.Clock()

# Load the sprite sheet
SPRITESHEET = pygame.image.load("levi.png").convert_alpha()
SPRITESHEET2 = pygame.image.load("mikasa.png").convert_alpha()

# Frame dimensions
frame_width = 61
frame_height = 58

# Character setup
character = pygame.sprite.Sprite()
character.image = SPRITESHEET.subsurface(0, 0, frame_width, frame_height)
character.image = pygame.transform.scale(character.image, (frame_width * 2, frame_height * 2))
character.rect = character.image.get_rect()
character.rect.x = 0
character.rect.y = 0

# Animation states and frame counts
idle_frames = 3
walk_frames = 1
grapple_frames = 1
IDLE = 0
WALK = 1
GRAPPLE = 2

# Animation control
character.current_state = IDLE
character.facing_right = True
character.current_frame = 0
character.animation_timer = 0
character.animation_speed = 10

running = True
while running:
    pressed_keys = pygame.key.get_pressed()

    # Handle character movement state
    if pressed_keys[pygame.K_SPACE]:
        character.current_state = GRAPPLE
    elif pressed_keys[pygame.K_s] or pressed_keys[pygame.K_w] or pressed_keys[pygame.K_a] or pressed_keys[pygame.K_d]:
        character.current_state = WALK
    else:
        character.current_state = IDLE

    # --- character Animation Update ---
    character.animation_timer += 1
    if character.animation_timer >= character.animation_speed:
        character.animation_timer = 0
        character.current_frame += 1

        if character.current_state == IDLE:
            if character.current_frame >= idle_frames:
                character.current_frame = 0
            frame_x = character.current_frame * frame_width
            frame_y = 0

        elif character.current_state == WALK:
            character.current_frame = 0  # Only one walk frame
            frame_x = 0
            frame_y = frame_height

        elif character.current_state == GRAPPLE:
            character.current_frame = 0  # Only one grapple frame
            frame_x = frame_width
            frame_y = frame_height

        # Update character image
        character.image = SPRITESHEET.subsurface(frame_x, frame_y, frame_width, frame_height)
        character.image = pygame.transform.scale(character.image, (frame_width * 2, frame_height * 2))
        if not character.facing_right:
            character.image = pygame.transform.flip(character.image, True, False)

    for event in pygame.event.get():
        if event.type == pygame.QUIT:
            running = False

    # Movement controls
    if pressed_keys[pygame.K_s] and character.rect.y < SCREEN_HEIGHT - 20:
        character.rect.move_ip(0, 4)
    if pressed_keys[pygame.K_w] and character.rect.y > 0:
        character.rect.move_ip(0, -4)
    if pressed_keys[pygame.K_a] and character.rect.x > 0:
        character.rect.move_ip(-4, 0)
        character.facing_right = False
    if pressed_keys[pygame.K_d] and character.rect.x < SCREEN_WIDTH - 20:
        character.rect.move_ip(4, 0)
        character.facing_right = True

    screen.fill((0, 0, 0))
    screen.blit(character.image, character.rect)
    pygame.display.flip()
    clock.tick(60)

pygame.quit()
